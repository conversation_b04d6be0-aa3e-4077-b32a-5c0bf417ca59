{"identifier": "D3BA00B1", "nonRenewingSubscriptions": [], "products": [{"displayPrice": "4.99", "familyShareable": false, "internalID": "9ADFF73F", "localizations": [{"description": "", "displayName": "", "locale": "en_US"}], "productID": "com.idea2App.nagy_offers_discounts.test", "referenceName": "Test", "type": "Consumable"}], "settings": {"_failTransactionsEnabled": false, "_locale": "en_US", "_storefront": "USA", "_storeKitErrors": [{"current": null, "enabled": false, "name": "Load Products"}, {"current": null, "enabled": false, "name": "Purchase"}, {"current": null, "enabled": false, "name": "Verification"}, {"current": null, "enabled": false, "name": "App Store Sync"}, {"current": null, "enabled": false, "name": "Subscription Status"}, {"current": null, "enabled": false, "name": "App Transaction"}, {"current": null, "enabled": false, "name": "Manage Subscriptions Sheet"}, {"current": null, "enabled": false, "name": "Refund Request Sheet"}, {"current": null, "enabled": false, "name": "Offer Code Redeem Sheet"}]}, "subscriptionGroups": [], "version": {"major": 3, "minor": 0}}