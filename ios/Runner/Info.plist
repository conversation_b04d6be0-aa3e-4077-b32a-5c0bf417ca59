<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleAllowMixedLocalizations</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>${appName}</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
			<string>hi</string>
			<string>es</string>
			<string>fr</string>
			<string>ar</string>
			<string>ru</string>
			<string>id</string>
			<string>ja</string>
			<string>ko</string>
			<string>vi</string>
			<string>ro</string>
			<string>tr</string>
			<string>it</string>
			<string>de</string>
			<string>pt</string>
			<string>hu</string>
			<string>he</string>
			<string>th</string>
			<string>nl</string>
			<string>sr</string>
			<string>pl</string>
			<string>fa</string>
			<string>uk</string>
			<string>bn</string>
			<string>ta</string>
			<string>cs</string>
			<string>sv</string>
			<string>fi</string>
			<string>el</string>
			<string>km</string>
			<string>kn</string>
			<string>mr</string>
			<string>ms</string>
			<string>bs</string>
			<string>lo</string>
			<string>sk</string>
			<string>sw</string>
			<string>zh</string>
			<string>my</string>
			<string>sq</string>
		</array>
		<key>CFBundleName</key>
		<string>${appName}</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>${websiteDomain}</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>${customScheme}</string>
					<string>${facebookLoginProtocolScheme}</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>${googleReversedClientId}</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>firebaseDynamicLink</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>${iosBundleId}</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>paytmMID</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>ENVATO_PURCHASE_CODE</key>
		<string>${envatoPurchaseCode}</string>
		<key>FacebookAppID</key>
		<string>${facebookAppId}</string>
		<key>FacebookClientToken</key>
		<string>${facebookClientToken}</string>
		<key>FacebookDisplayName</key>
		<string>${appName}</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false/>
		<key>FirebaseDeepLinkPasteboardRetrievalEnabled</key>
		<false/>
		<key>FirebaseScreenReportingEnabled</key>
		<true/>
		<key>GADApplicationIdentifier</key>
		<string>${adMobAppIdIos}</string>
		<key>GOOGLE_API_KEY</key>
		<string>${googleApiKeyIos}</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>paytm</string>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbauth</string>
			<string>fbauth2</string>
			<string>fb-messenger-api20140430</string>
			<string>comgooglemapsurl</string>
			<string>comgooglemaps</string>
			<string>tel</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
			<key>NSAllowsArbitraryLoadsInWebContent</key>
			<true/>
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>This app requires access to the camera for QR Code or Barcode scanner feature which can be access from the search bar</string>
		<key>NSFaceIDUsageDescription</key>
		<string>This app requires access to the Touch ID and Face ID to login in the app</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Get your current location for delivery</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Need current location access</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Get your current location for delivery</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>We would like access to the microphone to allow recording voice messages</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>We would like access to store Image/Videos in your Photo Library</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This app requires access to the photo library to select a profile photo which will be used as a chat image for the Chat Feature.</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>This application needs the speech recognition permission</string>
		<key>SKAdNetworkItems</key>
		<array>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>cstr6suwn9.skadnetwork</string>
			</dict>
		</array>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>io.flutter.embedded_views_preview</key>
		<string>YES</string>
	</dict>
</plist>
