import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flux_firebase/index.dart';
import 'package:gms_check/gms_check.dart';
import 'package:http/http.dart' as http;

import '../common/config.dart';
import '../common/constants.dart';
import '../firebase_options.dart';
import '../models/entities/firebase_error_exception.dart';
import '../models/entities/user.dart';
import 'firebase/index.dart';
import 'index.dart';
import 'notifications_service.dart';

class FirebaseServices extends BaseFirebaseServices {
  static final FirebaseServices _instance = FirebaseServices._internal();

  factory FirebaseServices() => _instance;

  FirebaseServices._internal();

  bool _isEnabled = false;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> init() async {
    var startTime = DateTime.now();
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    _isEnabled = kAdvanceConfig.enableFirebase;

    /// Not require Play Services
    /// https://firebase.google.com/docs/android/android-play-services
    _auth = FirebaseServiceFactory.create<FirebaseAuthService>();

    // _firestore = FirebaseFirestore.instance;

    if (Configurations.enableFirebaseAnalytics) {
      _firebaseAnalytics =
          FirebaseServiceFactory.create<FirebaseAnalyticsService>()!..init();
    } else {
      _firebaseAnalytics = FirebaseAnalyticsService()..init();
    }

    if (!kIsWeb) {
      _remoteConfig = FirebaseServiceFactory.create<FirebaseRemoteServices>();
    }

    /// Require Play Services
    const message = '[FirebaseServices] Init successfully';
    if (GmsCheck().isGmsAvailable) {
      _messaging = FirebaseMessaging.instance;
      _dynamicLinks = FirebaseServiceFactory.create<DynamicLinkService>();
      printLog(message, startTime);
    } else {
      printLog('$message (without Google Play Services)', startTime);
    }
  }

  /// Firebase Auth
  FirebaseAuthService? _auth;

  FirebaseAuthService? get auth => _auth;

  /// Firebase Cloud Firestore
  // FirebaseFirestore? _firestore;

  // FirebaseFirestore? get firestore => _firestore;

  /// Firebase Dynamic Links
  DynamicLinkService? _dynamicLinks;

  DynamicLinkService? get dynamicLinks => _dynamicLinks;

  /// Firebase Remote Config
  FirebaseRemoteServices? _remoteConfig;

  FirebaseRemoteServices? get remoteConfig => _remoteConfig;

  /// Firebase Messaging
  FirebaseMessaging? _messaging;

  FirebaseMessaging? get messaging => _messaging;

  /// Firebase Analytics
  FirebaseAnalyticsService? _firebaseAnalytics;

  FirebaseAnalyticsService? get firebaseAnalytics => _firebaseAnalytics;

  @override
  void deleteAccount() {
    _messaging?.deleteToken();
    _auth?.deleteAccount();
  }

  @override
  void loginFirebaseApple({authorizationCode, identityToken}) {
    if (FirebaseServices().isEnabled) {
      _auth?.loginFirebaseApple(
          authorizationCode: authorizationCode, identityToken: identityToken);
    }
  }

  @override
  void loginFirebaseFacebook({token}) async {
    if (FirebaseServices().isEnabled) {
      _auth?.loginFirebaseFacebook(token: token);
    }
  }

  @override
  void loginFirebaseGoogle({token}) async {
    if (FirebaseServices().isEnabled) {
      _auth?.loginFirebaseGoogle(token: token);
    }
  }

  @override
  void loginFirebaseEmail({email, password}) async {
    if (FirebaseServices().isEnabled) {
      _auth?.loginFirebaseEmail(email: email, password: password);
    }
  }

  @override
  Future<User?>? loginFirebaseCredential({credential}) {
    return _auth!.loginFirebaseCredential(credential: credential);
  }

  @override
  void saveUserToFirestore({user}) async {
    final token = await FirebaseServices().messaging!.getToken();
    printLog('tokendasdsadsad: $token');
    // await FirebaseServices()
    //     .firestore!
    //     .collection('users')
    //     .doc(user!.email)
    //     .set(
    //   {'deviceToken': token, 'isOnline': true},
    //   SetOptions(merge: true),
    // );

    if (GmsCheck().isGmsAvailable) {
      try {
        await Services()
            .api
            .updateUserInfo({'device_token': token}, user!.cookie);
      } catch (err, trace) {
        printError(err, trace);
      }
    }
  }

  @override
  dynamic getFirebaseCredential({verificationId, smsCode}) {
    return _auth?.getFirebaseCredential(
      verificationId: verificationId,
      smsCode: smsCode,
    );
  }

  @override
  StreamController<dynamic>? getFirebaseStream() {
    return _auth!.getFirebaseStream();
  }

  @override
  Future<void> verifyPhoneNumber({
    phoneNumber,
    codeAutoRetrievalTimeout,
    codeSent,
    required void Function(String?) verificationCompleted,
    void Function(FirebaseErrorException error)? verificationFailed,
    forceResendingToken,
    Duration? timeout,
  }) async {
    await _auth!.verifyPhoneNumber(
      phoneNumber: phoneNumber!,
      codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
      codeSent: codeSent,
      timeout: timeout ?? const Duration(seconds: 120),
      verificationCompleted: verificationCompleted,
      verificationFailed: verificationFailed,
      forceResendingToken: forceResendingToken,
    );
  }

  @override
  Widget renderChatAuthScreen() {
    return const SizedBox();

    // return const ChatAuth();
  }

  @override
  Widget renderListChatScreen() {
    return const SizedBox();

    // return RealtimeChat(
    //   userEmail: Configurations.adminEmail,
    //   type: RealtimeChatType.adminToCustomers,
    // );
  }

  @override
  Widget renderVendorListChatScreen({String? email}) {
    return const SizedBox();

    // if (email == null) {
    //   return const ChatAuth();
    // }
    // return RealtimeChat(
    //   userEmail: email,
    //   type: RealtimeChatType.vendorToCustomers,
    // );
  }

  @override
  Widget renderChatScreen({User? senderUser, receiverEmail, receiverName}) {
    return const SizedBox();

    // final isMvApp =
    //     ServerConfig().isVendorType() || ServerConfig().isVendorManagerType();
    //
    // final email = senderUser?.email;
    // if (senderUser == null || email == null) {
    //   return const ChatAuth();
    // }
    //
    // /// MV: Customer to Vendor.
    // if (isMvApp &&
    //     senderUser.email != receiverEmail &&
    //     receiverEmail != null &&
    //     receiverName != null) {
    //   return RealtimeChat(
    //     type: RealtimeChatType.customerToVendor,
    //     vendorName: receiverName,
    //     vendorEmail: receiverEmail,
    //     userEmail: email,
    //   );
    // }
    //
    // /// MV: Vendor to self or Vendor to Customer.
    // final isVendorChatToSelf = senderUser.email == receiverEmail;
    // if (isMvApp &&
    //     senderUser.isVender &&
    //     (isVendorChatToSelf || senderUser.email != receiverEmail)) {
    //   return RealtimeChat(
    //     userEmail: email,
    //     type: RealtimeChatType.vendorToCustomers,
    //   );
    // }
    //
    // /// Admin to Customers.
    // if (!isMvApp && senderUser.email == Configurations.adminEmail) {
    //   return RealtimeChat(
    //     userEmail: email,
    //     type: RealtimeChatType.adminToCustomers,
    //   );
    // }
    //
    // /// Default: Customer to Admin.
    // return RealtimeChat(
    //   userEmail: email,
    //   type: RealtimeChatType.customerToAdmin,
    // );
  }

  @override
  void createUserWithEmailAndPassword({email, password}) {
    if (isEnabled) {
      _auth?.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
    }
  }

  // @override
  // User? getCurrentUser() {
  //   try {
  //     return FirebaseServices().auth?.currentUser;
  //   } catch (e) {
  //     printLog('[Tabbar] getCurrentUser error ${e.toString()}');
  //   }
  // }

  @override
  Future<String?> getMessagingToken() async {
    return await messaging?.getToken();
  }

  @override
  void initDynamicLinkService(context) {
    _dynamicLinks?.initDynamicLinks(context);
  }

  @override
  Future<bool> loadRemoteConfig() {
    return _remoteConfig?.loadRemoteConfig() ?? Future.value(false);
  }

  @override
  Future<List<String>> getRemoteKeys() async {
    return await _remoteConfig?.getKeys() ?? [];
  }

  @override
  String getRemoteConfigString(String key) {
    return _remoteConfig?.getString(key) ?? '';
  }

  @override
  void shareDynamicLinkProduct({itemUrl}) {
    _dynamicLinks?.shareProductLink(
      productUrl: itemUrl,
    );
  }

  @override
  Future<void> signOut() async {
    if (isEnabled) {
      _auth?.signOut();
    }
  }

  @override
  List<NavigatorObserver> getMNavigatorObservers() {
    return firebaseAnalytics?.getMNavigatorObservers() ?? <NavigatorObserver>[];
  }

  //! Send Notification
  Future<void> sendNotification({
    required String title,
    required String body,
    required String userToken,
    bool isTopic = false,
  }) async {
    const firebaseProjectId = 'idea2app-dev';

    const fcmUrl =
        'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';

    printLog('fcmUrl: $userToken');

    final accessToken = await AccessTokenFirebase().getAccessToken();

    printLog('accessToken: $accessToken');

    final message = <String, dynamic>{
      'message': {
        'token': isTopic ? '/topics/$userToken' : userToken,
        'notification': {
          'title': title,
          'body': body,
        },
      }
    };

    final response = await http.post(
      Uri.parse(fcmUrl),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode(message),
    );

    if (response.statusCode == 200) {
      printLog('NotificationSentSuccessfully ${response.body}');
    } else {
      printLog('Failed to send notification: ${response.statusCode}');
      printLog(response.body);
    }
  }

// Future sendNotification({
//   required String title,
//   required String body,
//   required String userToken,
//   bool isTopic = false,
// }) async {
//   final data = jsonEncode(
//     <String, dynamic>{
//       'notification': <String, dynamic>{
//         'title': title,
//         'body': body,
//       },
//       'priority': 'high',
//       'data': <String, dynamic>{
//         'click_action': 'FLUTTER_NOTIFICATION_CLICK',
//         'id': '1',
//         'status': 'done'
//       },
//       'to': isTopic ? '/topics/$userToken' : userToken,
//     },
//   );
//
//   final response = await http.post(
//     Uri.parse('https://fcm.googleapis.com/fcm/send'),
//     headers: <String, String>{
//       'Content-Type': 'application/json',
//       'Authorization': 'key=${Configurations.serverKey}',
//     },
//     body: data,
//   );
//
//   log('notificationResponseDDATAA: ${response.body}');
// }
}
