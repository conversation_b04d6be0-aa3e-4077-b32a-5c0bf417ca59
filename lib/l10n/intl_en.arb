{"@@locale": "en", "seeAll": "See All", "featureProducts": "Feature Products", "bagsCollections": "Gears Collections", "womanCollections": "Woman Collections", "manCollections": "Man Collections", "buyNow": "Buy Now", "off": "Off", "products": "Cars", "addToCart": "Add To Cart ", "promoCodeNotFound": "Promo code not found", "description": "Description", "totalProductsNumber": "{total} Cars", "totalProductItems": "Total Cars: {total}", "totalPriceNumber": "Total price: {total}", "readReviews": "Reviews", "additionalInformation": "Additional Information", "noReviews": "No Reviews", "productAdded": "The product is added", "youMightAlsoLike": "You might also like", "selectTheSize": "Select the item", "selectTheColor": "Select the color", "selectTheQuantity": "Select the quantity", "selectTheFile": "Select the file", "size": "Size", "color": "Color", "myCart": "My Cart", "saveToWishList": "Save to Wishlist", "share": "Share", "cancel": "Cancel", "checkout": "Checkout", "clearCart": "Clear Cart", "myWishList": "My Wishlist", "myOrder": "My Orders", "yourBagIsEmpty": "Your bag is empty", "emptyCart": "Empty cart", "emptyCartSubtitle": "Looks like you haven’t added any items to the bag yet. Start shopping to fill it in.", "emptyCartSubtitle02": "Oops! Your cart is feeling a bit light. \n\nReady to shop for something fabulous?", "startShopping": "Start Shopping", "addToWishlist": "Add to Wishlist", "noFavoritesYet": "No favorites yet.", "emptyWishlist": "Empty wishlist", "emptyWishlistSubtitle": "Tap any heart next to a product to favorite. We’ll save them for you here!", "emptyWishlistSubtitle02": "Your wishlist is currently empty.\nStart adding Cars now!", "searchForItems": "Search for Items", "shipping": "Shipping", "preview": "preview", "review": "Review", "productOverview": "Product overview", "payment": "Payment", "firstName": "First Name", "lastName": "Last Name", "city": "Area", "stateProvince": "City", "zipCode": "Floor", "country": "Country", "phoneNumber": "Phone number", "email": "Email", "streetName": "Street Name", "shippingMethod": "Shipping Method", "continueToShipping": "Continue to Shipping", "continueToReview": "Continue to Preview", "continueToPayment": "Continue to Payment", "goBackToAddress": "Go back to address", "goBackToShipping": "Go back to shipping", "goBackToReview": "Go back to review", "address": "Address", "shippingAddress": "Shipping Address", "orderDetail": "Order Details", "viewOrder": "View order", "continueShopping": "Continue shopping", "yourOrderIsConfirmed": "Your Order is Confirmed!", "subTitleOrderConfirmed": "Thanks for your order. We're working quickly to process your order.", "subtotal": "Subtotal", "total": "Total", "paymentMethods": "Payment Methods", "chooseYourPaymentMethod": "Choose your payment method", "placeMyOrder": "Place My Order", "itsOrdered": "It's ordered!", "orderNo": "Order No", "showAllMyOrdered": "Show All My Orders", "backToShop": "Back to Shop", "firstNameIsRequired": "The first name field is required", "lastNameIsRequired": "The last name field is required", "streetIsRequired": "The street name field is required", "cityIsRequired": "The city field is required", "stateIsRequired": "The state field is required", "countryIsRequired": "The country field is required", "phoneIsRequired": "The phone number field is required", "emailIsRequired": "The email field is required", "zipCodeIsRequired": "The zip code field is required", "noOrders": "No Orders", "orderDate": "Order Date", "status": "Status", "paymentMethod": "Payment Method", "orderHistory": "Orders History", "refundRequest": "Refund Request", "recentSearches": "Recent search results", "recents": "Recent", "byPrice": "By Price", "byCategory": "By Category", "noInternetConnection": "No internet connection", "connect": "Connect", "settings": "Settings", "generalSetting": "General Setting", "getNotification": "Get Notifications", "listMessages": "Notification Messages", "language": "Languages", "darkTheme": "Dark Theme", "rateTheApp": "Rate the app", "logout": "Logout", "login": "<PERSON><PERSON>", "items": "items", "cart": "<PERSON><PERSON>", "shop": "Shop", "search": "Search", "blog": "Blog", "apply": "Apply", "reset": "Reset", "signInWithEmail": "Sign in with email ", "dontHaveAccount": "Don't have an account?", "signup": "Sign up", "welcome": "Welcome", "close": "Close", "or": "OR", "pleaseInput": "Please fill in the required fields", "searchingAddress": "Search Address", "outOfStock": "Out of stock", "unavailable": "Unavailable", "category": "Category", "noProduct": "No Cars", "weFoundProducts": "We found {length} Cars", "clear": "Clear", "video": "Video", "recentView": "Your Recent View", "recentlyViewed": "Recently Viewed", "inStock": "In stock", "trackingNumberIs": "Tracking number is", "availability": "Availability", "trackingPage": "Tracking page", "myPoints": "My points", "youHavePoints": "You have $point points", "events": "Events", "date": "Date", "point": "Point", "orderNotes": "Order notes", "ratingFirst": "Please rating before you send your comment", "commentFirst": "Please write your comment", "writeComment": "Write your comment", "loading": "Loading...", "productRating": "Your rating", "layout": "Layouts", "selectAddress": "Select Address", "saveAddress": "Save Address", "addNewAddress": "Add new address", "searchInput": "Please write input in search field", "totalTax": "Total tax", "invalidSMSCode": "Invalid SMS Verification code", "sendSMSCode": "Get code", "verifySMSCode": "Verify", "showGallery": "Show Gallery", "discount": "Discount", "username": "Username", "password": "Password", "enterYourEmail": "Enter your email", "enterYourPassword": "Enter your password", "iwantToCreateAccount": "I want to create an account", "loginToYourAccount": "Login to your account", "createAnAccount": "Create an account", "couponCode": "Coupon code", "selectVoucher": "Select voucher", "descriptionEnterVoucher": "Please input or select the voucher for your order.", "enterVoucherCode": "Enter voucher code", "remove": "Remove", "couponMsgSuccess": "Congratulations! Coupon code applied successfully", "saveAddressSuccess": "Save address successfully", "yourNote": "Your note", "writeYourNote": "Write your note", "orderSuccessTitle1": "You've successfully placed the order", "orderSuccessMsg1": "You can check status of your order by using our delivery status feature.", "orderSuccessTitle2": "Your account", "orderSuccessMsg2": "You can log to your account using e-mail and password defined earlier. On your account you can edit your profile data, check history of orders.", "signIn": "Sign In", "signUp": "Sign Up", "updateProfile": "Update Profile", "next": "Next", "done": "Done", "currencies": "Currencies", "sale": "Sale {percent}%", "updateUserInfor": "Update Profile", "update": "Update", "aboutUs": "About Us", "support": "Support", "displayName": "Display name", "niceName": "Nice name", "english": "English", "vietnamese": "Vietnamese", "arabic": "Arabic", "spanish": "Spanish", "chinese": "Chinese", "japanese": "Japanese", "languageSuccess": "The Language is updated successfully", "agreeWithPrivacy": "Privacy and Term", "privacyAndTerm": "Privacy and Term", "iAgree": "I agree with", "categories": "Categories", "stores": "Stores", "visitStore": "Visit Store", "salePrice": "Sale price", "regularPrice": "Regular price", "imageGallery": "Image Gallery", "addingYourImage": "Adding your image", "postProduct": "Post Product", "createProduct": "Create Product", "waitForLoad": "Waiting for loading image", "waitForPost": "Waiting for posting product", "productName": "Product name", "productType": "Product type", "conversations": "Conversations", "myProducts": "My Cars", "myProductsEmpty": "You don't have any Cars. Try to create one!", "product": "Product", "contact": "Contact", "currentPassword": "Current Password", "newPassword": "New Password", "addToCartSucessfully": "Added to cart successfully", "pullToLoadMore": "Pull to Load more", "loadFail": "Load Failed!", "releaseToLoadMore": "Release to load more", "viewMore": "View more", "noData": "No more Data", "all": "All", "filter": "Filter", "tags": "Tags", "attributes": "Attributes", "resetPassword": "Reset Password", "resetYourPassword": "Reset Your Password", "yourUsernameEmail": "Your username or email", "getPasswordLink": "Get password link", "checkConfirmLink": "Check your email for confirmation link", "emptyUsername": "Username/Email is empty", "romanian": "Romanian", "turkish": "Turkish", "italian": "Italian", "indonesian": "Indonesian", "german": "German", "couponInvalid": "Your coupon code is invalid", "featured": "Featured", "onSale": "On Sale", "pleaseCheckInternet": "Please checking internet connection!", "canNotLaunch": "Cannot launch this app, make sure your settings on config.dart is correct", "message": "Message", "billingAddress": "Billing Address", "noAddressHaveBeenSaved": "No addresses have been saved yet.", "no": "No", "yes": "Yes", "areYouSure": "Are you sure?", "doYouWantToExitApp": "Do you want to exit the App", "shoppingCartItems": "Shopping cart, {totalCartQuantity} items", "orderStatusOnHold": "On hold", "orderStatusPendingPayment": "Pending Payment", "orderStatusFailed": "Failed", "orderStatusProcessing": "Processing", "orderStatusPending": "Pending", "orderStatusCompleted": "Completed", "orderStatusCancelled": "Cancelled", "orderStatusRefunded": "Refunded", "pleaseFillCode": "Please fill your code", "warning": "Warning: {message}", "nItems": "{itemCount} items", "dataEmpty": "Data Empty", "yourAddressExistYourLocal": "Your address exists in your local", "ok": "OK", "yourAddressHasBeenSaved": "The address has been saved to your local storage", "undo": "Undo", "thisPlatformNotSupportWebview": "This platform is not support for webview", "noBackHistoryItem": "No back history item", "noForwardHistoryItem": "No forward history item", "dateBooking": "Date Booking", "duration": "Duration", "addedSuccessfully": "Added Successfully", "notFound": "Not Found", "error": "Error: {message}", "goBackHomePage": "Go back to home page", "noBlog": "Op<PERSON>, the blog seems no longer exist", "prev": "Prev", "skip": "<PERSON><PERSON>", "download": "Download", "downloadApp": "Download App", "daysAgo": "{day} days ago", "hoursAgo": "{hour} hours ago", "minutesAgo": "{minute} minutes ago", "secondsAgo": "{second} seconds ago", "rateThisApp": "Rate this app", "rateThisAppDescription": "If you like this app, please take a little bit of your time to review it !\nIt really helps us and it shouldn't take you more than one minute.", "rate": "Rate", "noThanks": "No thanks", "maybeLater": "Maybe Later", "phone": "Phone", "phoneNumberVerification": "Phone Number Verification", "enterSendedCode": "Enter the code sent to", "pleasefillUpAllCellsProperly": "*Please fill up all the cells properly", "didntReceiveCode": "Didn't receive the code? ", "resend": " RESEND", "pleaseInputFillAllFields": "Please fill in all fields", "pleaseAgreeTerms": "Please agree with our terms", "url": "URL", "nearbyPlaces": "Nearby Places", "noResultFound": "No Result Found", "searchPlace": "Search Place", "searchResultItems": "{keyword} ({count} items)", "searchResultItem": "{keyword} ({count} item)", "searchResultFor": "Search results for: '{keyword}'", "tapSelectLocation": "Tap to select this location", "brazil": "Portuguese", "backOrder": "On backorder", "french": "French", "loginErrorServiceProvider": "There is an issue with the app during request the data, please contact admin for fixing the issues: {message}", "loginCanceled": "The login is cancelled", "noPost": "<PERSON><PERSON>, this page seems no longer exist!", "minimumQuantityIs": "Minimum quantity is", "youCanOnlyPurchase": "You can only purchase", "forThisProduct": "for this product", "currentlyWeOnlyHave": "Currently we only have", "ofThisProduct": "of this product", "from": "From", "totalCartValue": "Total order's value must be at least", "hungary": "Hungarian", "streetNameApartment": "Apartment", "streetNameBlock": "Building", "byTag": "By Tag", "transactionCancelled": "Transaction cancelled", "tax": "Tax", "soldBy": "Sold by", "shopOrders": "Shop Orders", "refresh": "Refresh", "sku": "SKU", "pointRewardMessage": "There is the Discount Rule for applying your points to <PERSON><PERSON>", "availablePoints": "Your available points: {point}", "selectThePoint": "Select the point", "cartDiscount": "<PERSON><PERSON>unt", "pleaseSelectAllAttributes": "Please choose an option for each attribute of the product", "booking": "Booking", "bookingError": "There is something wrong. Please try again later.", "bookingNow": "Book Now", "bookingSuccess": "Successfully Booked", "booked": "Already booked", "waitingForConfirmation": "Waiting for confirmation", "bookingConfirm": "Confirmed", "bookingCancelled": "Booking Cancel", "bookingUnavailable": "Booking is unavailable", "bookingSummary": "Booking Summary", "dateEnd": "Date End", "dateStart": "Started Date", "tickets": "Tickets", "requestBooking": "Request Booking", "extraServices": "Extra Services", "guests": "Guests", "hour": "Hour", "features": "Features", "removeFromWishList": "Remove from WishList", "map": "Map", "menus": "Menus", "prices": "Prices", "totalPrice": "Total price", "addListing": "Add Listing", "bookingHistory": "Booking History", "vendorAdmin": "<PERSON><PERSON><PERSON>", "russian": "Russian", "pickADate": "Pick Date & Time", "on": "on", "yourBookingDetail": "Your booking detail", "adults": "Adults", "additionalServices": "Additional services", "none": "None", "thisDateIsNotAvailable": "This date is not available", "noSlotAvailable": "No slot available", "payNow": "Pay Now", "sold": "Sold: {numberOfUnitsSold}", "almostSoldOut": "Almost sold out", "endsIn": "Ends in {timeLeft}", "hebrew": "Hebrew", "thailand": "Thai", "hungarian": "Hungarian", "vendorInfo": "Vendor Info", "netherlands": "dutch", "india": "Hindi", "useNow": "Use Now", "expired": "Expired", "validUntilDate": "Valid til {date}", "expiringInTime": "Expiring in {time}", "fixedCartDiscount": "Fixed Cart Discount", "fixedProductDiscount": "Fixed Product Discount", "couponHasBeenSavedSuccessfully": "Coupon has been saved successfully.", "saveForLater": "Save For Later", "refund": "Refund", "continues": "Continue", "egypt": "Egypt", "qty": "qty", "itemTotal": "Item total: ", "createdOn": "Created on", "orderId": "Order ID: ", "grossSales": "Gross Sales", "earnings": "Earnings", "allOrders": "Latest Sales", "yourEarningsThisMonth": "Your earnings this month", "searchOrderId": "Search with Order ID...", "yourOrders": "Your Orders", "editProductInfo": "Edit Product Info", "cantFindThisOrderId": "Can't find this order ID", "showDetails": "Detail", "orLoginWith": "or login with", "notifications": "Notifications", "back": "Back", "deliveredTo": "Delivered to", "orderTotal": "Order Total", "addProduct": "Add Product", "takePicture": "Take Picture", "chooseFromGallery": "<PERSON><PERSON> From Gallery", "chooseFromServer": "<PERSON><PERSON> From Server", "selectImage": "Select Image", "more": "...more", "uploadProduct": "Upload Product", "name": "Name", "stockQuantity": "Stock Quantity", "manageStock": "Manage stock", "shortDescription": "Short Description", "updateInfo": "Update Info", "stock": "Stock", "pending": "Pending", "delivering": "Delivering", "confirmed": "Confirmed", "approve": "Approve", "approved": "Approved", "rating": "rating", "change": "change", "reviewApproval": "Review Approval", "home": "Home", "reviews": "Reviews", "updateStatus": "Update Status", "chatListScreen": "Messages", "cardNumber": "Card number", "expiredDate": "Expired Date", "expiredDateHint": "MM/YY", "cvv": "CVV", "cardHolder": "Card Holder", "mustSelectOneItem": "Must select 1 item", "optionsTotal": "Options total: {price}", "options": "Options", "pleaseSelectRequiredOptions": "Please select required options!", "location": "Location", "send": "Send", "typeYourMessage": "Type your message here...", "dashboard": "Dashboard", "edit": "Edit: ", "editWithoutColon": "Edit", "thisFeatureDoesNotSupportTheCurrentLanguage": "This feature does not support the current language", "brand": "Brand", "reviewPendingApproval": "Your review has been sent and is waiting for approval!", "reviewSent": "Your review has been sent!", "publish": "Publish", "private": "Private", "draft": "Draft", "simple": "Simple", "grouped": "Grouped", "variable": "Variable", "active": "Active", "uploading": "Uploading", "uploadFile": "Upload file", "gallery": "Gallery", "files": "Files", "selectFileCancelled": "Select file cancelled!", "fileIsTooBig": "The file is too big. Please choose a smaller file!", "fileUploadFailed": "File upload failed!", "totalProducts": "{total} products", "addAName": "Add a name", "addAnAttr": "Add an attribute", "addNew": "Add new", "selectAll": "Select all", "selectNone": "Select none", "visible": "Visible", "variation": "Variation", "delete": "Delete", "direction": "Direction", "noListingNearby": "No listing nearby!", "noStoreNearby": "No store nearby!", "emailDoesNotExist": "The email account that you entered does not exist. Please try again.", "errorEmailFormat": "Please enter a valid email address.", "errorPasswordFormat": "Please enter a password of at least 8 characters", "confirmClearTheCart": "Are you sure you want to clear the cart?", "keep": "Keep", "serbian": "Serbian", "polish": "Polish", "persian": "Persian", "kurdish": "Kurdish", "pleaseSignInBeforeUploading": "Please sign in to your account before uploading any files.", "maximumFileSizeMb": "Maximum file size: {size} MB", "loginFailed": "Login failed!", "loginSuccess": "Login successfully!", "loginInvalid": "Can not login! Please contact the administrator to check your account/role.", "updateFailed": "Update failed!", "updateSuccess": "Update successfully!", "ukrainian": "Ukrainian", "bengali": "Bengali", "chatWithStoreOwner": "Chat with Store Owner", "chatViaWhatApp": "Chat via WhatsApp", "chatViaFacebook": "Chat via Facebook Messenger", "callTo": "Make a Call To", "messageTo": "Send Message To", "week": "Week {week}", "storeSettings": "Store Settings", "storeLogo": "Store Logo", "link": "Link", "shopName": "Shop name", "shopSlug": "Shop slug", "shopEmail": "Shop email", "shopPhone": "Shop phone", "bannerType": "Banner Type", "storeStaticBanner": "Store Static Banner", "storeSliderBanner": "Store Slider Banner", "bannerYoutubeURL": "Banner Youtube URL", "storeMobileBanner": "Store Mobile Banner", "bannerListType": "Banner List Type", "listBannerType": "List Banner Type", "listBannerVideo": "List Banner Video", "storeListBanner": "Store List Banner", "street": "Street", "street2": "Street 2", "hideEmail": "<PERSON><PERSON>", "hidePhone": "Hide Phone", "hideAddress": "Hide Address", "hideMap": "Hide Map", "hideAbout": "Hide About", "hidePolicy": "Hide Policy", "enterYourEmailOrUsername": "Enter your email or username", "enterYourFirstName": "Enter your first name", "enterYourLastName": "Enter your last name", "enterYourPhoneNumber": "Enter your phone number", "refundOrderSuccess": "Request a refund for your order successfully!", "addASlug": "Add a slug", "refundOrderFailed": "The request for a refund for the order was unsuccessful", "confirm": "Confirm", "confirmDeleteItem": "Are you sure you wish to delete this item?", "markAsRead": "<PERSON> as read", "markAsUnread": "<PERSON> as unread", "noFileToDownload": "No file to download.", "orderStatusShipped": "Shipped", "orderStatusReversed": "Reversed", "orderStatusCanceledReversal": "Canceled Reversal", "orderStatusChargeBack": "Charge Back", "orderStatusDenied": "Denied", "orderStatusExpired": "Expired", "orderStatusProcessed": "Processed", "orderStatusVoided": "Voided", "delivered": "Delivered", "incorrectPassword": "Incorrect password", "deliveryDetails": "Delivery Details", "assigned": "Assigned", "call": "Call", "fullName": "Full name", "chat": "Cha<PERSON>", "updatePassword": "Update password", "customerDetail": "Customer detail", "storeInformation": "Store Information", "markAsShipped": "Mark as shipped", "shipped": "Shipped", "productCreateReview": "Your product will show up after review.", "postSuccessfully": "Your post has been created succesfully", "postFail": "Your post is failed to be created", "postTitle": "Title", "postContent": "Content", "postImageFeature": "Featured Image", "submitYourPost": "Submit Your Post", "postManagement": "Post Management", "addNewPost": "Create New Post", "monthsAgo": "{month} months ago", "yearsAgo": "{year} years ago", "weFoundBlogs": "We Found Blog(s)", "startExploring": "Start Exploring", "exploreNow": "Explore now", "commentSuccessfully": "Comment successfully, please wait until your comment is approved", "loginToComment": "Please Login To Comment", "pageView": "Page View", "addNewBlog": "Add New Blog", "momentAgo": "a moment ago", "webView": "Web View", "privacyPolicy": "Privacy Policy", "addANewPost": "Add A New Post", "title": "Title", "content": "Content", "imageFeature": "Featured Image", "submit": "Submit", "createNewPostSuccessfully": "Your post has been successfully created as a draft. Please take a look at your admin site.", "emptySearch": "You haven't searched for items yet. Let's start now - we'll help you.", "createPost": "Create post", "emptyComment": "Your comment can not be empty", "hindi": "Hindi", "korean": "korean", "dutch": "dutch", "relatedLayoutTitle": "Related blogs", "audioDetected": "Audio item(s) detected. Do you want to add to Audio Player?", "dateASC": "Date ascending", "dateDESC": "Date descending", "seeOrder": "See Order", "openMap": "Map", "allDeliveryOrders": "All Orders", "orderSummary": "Order Summary", "note": "Order Note", "searchByName": "Search with Name...", "orderIdWithoutColon": "Order ID", "deliveryNotificationError": "No Data.\nThis order has been removed.", "deliveryManagement": "Delivery", "deliveryBoy": "Delivery Boy:", "recurringTotals": "Recurring Totals", "firstRenewal": "First Renewal", "atLeastThreeCharacters": "At least 3 characters...", "popular": "Popular", "latestProducts": "Latest Products", "seeReviews": "See reviews", "deliveryDate": "Delivery Date", "youHaveAssignedToOrder": "You have assigned to order #{total}", "distance": "~{total} km", "registerFailed": "Register failed", "registerSuccess": "Register successfully", "invalidPhoneNumber": "Invalid Phone Number", "requestTooMany": "You have requested too many codes in a short time. Please try again later.", "phoneEmpty": "Phone is empty", "choosePlan": "Choose <PERSON>", "recommended": "Recommended", "paidStatus": "Paid status", "paid": "Paid", "unpaid": "Unpaid", "mobileVerification": "Mobile Verification", "enterYourMobile": "Please enter your mobile number", "phoneHintFormat": "Format: +***********", "verificationCode": "Verification code (6 digits)", "userExists": "This username/email is not available.", "accountSetup": "Account setup", "youNotBeAsked": "You won't be asked next time after the completion", "openNow": "Open now", "closeNow": "Closed now", "storeClosed": "The store is closed now", "comment": "Comment", "firstComment": "Be the first one commenting on this post!", "greaterDistance": ">{total} km", "addToCartMaximum": "The maximum quantity has been exceeded", "playAll": "Play All", "customerNote": "Customer note", "stop": "Stop", "youCanOnlyOrderSingleStore": "You can only purchase from a single store.", "instantlyClose": "Instantly close", "dateWiseClose": "Date wise close", "enableVacationMode": "Enable vacation mode", "disablePurchase": "Disable purchase", "vacationType": "Vacation type", "selectDates": "Select dates", "vacationMessage": "Vacation Message", "storeVacation": "Store vacation", "cantPickDateInThePast": "Date in the past is not allowed", "endDateCantBeAfterFirstDate": "Please select a date after first date", "onVacation": "On vacation", "refundRequested": "Refund Requested", "myWallet": "My Wallet", "viaWallet": "Via wallet", "payByWallet": "Pay by wallet", "lastTransactions": "Last Transactions", "doNotAnyTransactions": "You don't have any transactions yet", "topUp": "Top Up", "transfer": "Transfer", "historyTransaction": "History", "topUpProductNotFound": "Top Up product not found", "transactionResult": "Transaction Result", "transferFailed": "Transfer failed", "errorAmountTransfer": "Entered amount is greater than current wallet amount. Please try again!", "backToWallet": "Back to Wallet", "transferSuccess": "Transfer success", "viewRecentTransactions": "View recent transactions", "amount": "Amount", "noteTransfer": "Note (optional)", "transferConfirm": "Transfer Confirmation", "balance": "Balance", "confirmClearCartWhenTopUp": "The cart will be cleared when top up.", "confirmRemoveProductInCart": "Are you sure you want to remove this product?", "warningCurrencyMessageForWallet": "The currently selected currency is not available for the Wallet feature, please change it to {defaultCurrency}", "transferErrorMessage": "You can't transfer to this user", "czech": "Czech", "chooseCategory": "Choose category", "chooseType": "Choose type", "external": "External", "pleaseChooseCategory": "Please choose category", "pleaseAddPrice": "Please add price", "pleaseEnterProductName": "Please enter the product name", "hasBeenDeleted": "has been deleted", "basicInformation": "Basic Information", "storeEmail": "Shop Email", "pointMsgMaximumDiscountPoint": "Maximum discount point", "pointMsgOverMaximumDiscountPoint": "You have reach maximum discount point", "pointMsgSuccess": "Discount point is applied successfully", "pointMsgRemove": "Discount point is removed", "pointMsgEnter": "Please enter discount point", "prepaid": "Prepaid", "pointMsgOverTotalBill": "The total discount value is over the bill  total", "pointMsgConfigNotFound": "There is no discount point configuration has been found in server", "pointMsgNotEnough": "You don't have enough discount point. Your total discount point is", "scannerLoginFirst": "To scan an order, you need to login first", "scannerCannotScan": "This item cannot be scanned", "scannerOrderAvailable": "This order is not available for your account", "storeAddress": "Shop Address", "storeBrand": "Store Brand", "storeLocation": "Store Location", "pleaseSelectImages": "Please select images", "pleaseSelectALocation": "Please select a location", "storeBanner": "Banner", "finishSetup": "Finish setup", "isEverythingSet": "Is everything set...?", "getStarted": "Get Started", "online": "Online", "somethingWrong": "Something went wrong. Please try again later.", "chooseStaff": "Choose <PERSON>", "morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "expectedDeliveryDate": "Expected Delivery Date", "qtyTotal": "Qty: {total}", "added": "Added", "reOrder": "Re-order", "yourOrderHasBeenAdded": "You order has been added", "swedish": "Swedish", "finnish": "Finnish", "greek": "Greek", "tamil": "Tamil", "khmer": "Khmer", "pleaseSelectADate": "Please select a booking date", "allBrands": "All Brands", "kannada": "Kannada", "marathi": "Marathi", "history": "History", "favorite": "Favorite", "orders": "Orders", "state": "State", "save": "Save", "addToOrder": "Add to order", "invoice": "Invoice", "complete": "Complete", "cash": "Cash", "malay": "Malay", "bosnian": "Bosnian", "allProducts": "All Products", "lao": "Lao", "slovak": "Slovak", "swahili": "Swahili", "posAddressToolTip": "This address will be saved to your local device. It is NOT the user address.", "usernameAndPasswordRequired": "Username and password are required", "loginToContinue": "Please login to continue", "doYouWantToLogout": "Do you want to logout?", "setAnAddressInSettingPage": "Please set an address in settings page", "receivedMoney": "Received money", "debit": "Debit", "transactionDetail": "Transaction detail", "paymentSuccessful": "Payment successful", "time": "Time", "transactionFee": "Transaction fee", "freeOfCharge": "Free of charge", "walletBalance": "Wallet balance", "moreInformation": "More information", "walletName": "Wallet name", "noteMessage": "note", "sendBack": "Send back", "noPrinters": "No Printers", "select": "Select", "checking": "Checking...", "printing": "Printing...", "turnOnBle": "Turn On Bluetooth", "dateTime": "Date Time", "orderNumber": "Order Number", "printReceipt": "Print Receipt", "printerSelection": "Printer Selection", "printerNotFound": "The printer not found", "item": "<PERSON><PERSON>", "countItem": "{count} item", "countItems": "{count} items", "bleState": "Bluetooth Adapter is {state}", "printer": "Printer", "changePrinter": "Change Printer", "selectPrinter": "Select Printer", "bleHasNotBeenEnabled": "Bluetooth has not been enabled", "attributeAlreadyExists": "Attribute already exists", "deleteAll": "Delete all", "createVariants": "Create all variants", "anyAttr": "Any {attribute}", "newVariation": "New variation", "yourProductIsUnderReview": "Your product is under review", "orderConfirmation": "Order Confirmation", "orderConfirmationMsg": "Are you sure to create the order?", "thisProductNotSupport": "This product is not support", "pleaseSelectAttr": "Please select at least 1 variation attribute for each active attribute", "yourApplicationIsUnderReview": "Your application is under review.", "chineseSimplified": "Chinese (simplified)", "chineseTraditional": "Chinese (traditional)", "receiver": "Receiver", "burmese": "Burmese", "albanian": "Albanian", "productNeedAtLeastOneVariation": "Product type variable needs at least one variant", "productNeedNameAndPrice": "Product type simple needs the name and regular price", "sortBy": "Sort by", "dateLatest": "Date: Latest", "dateOldest": "Date: Oldest", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "loadingLink": "Loading link...", "canNotLoadThisLink": "The link is currently unavailable on this site.", "imageNetwork": "Image Network", "pasteYourImageUrl": "Paste your image url", "useThisImage": "Use this Image", "failedToLoadImage": "Failed to load image", "allow": "Allow", "decline": "Decline", "agree": "Agree", "notifyLatestOffer": "Notify latest offers & product availability", "weWillSendYouNotification": "We will send you notifications when new products are available or offers are available. You can always turn it off in the settings.", "bySignup": "By signing up, you agree to our ", "deleteAccount": "Delete Account", "areYouSureDeleteAccount": "Are you sure to delete your account?", "deleteAccountMsg": "Are you sure you want to delete your account? Please read how account deletion will affect.", "accountDeleteDescription": "Deleting your account removes personal information from our database.", "emailDeleteDescription": "Deleting your account will unsubscribe you from all mailing lists.", "enterCaptcha": "Enter {captcha} to confirm:", "account": "Account", "emailSubscription": "Email Subscription", "confirmAccountDeletion": "Confirm Account Deletion", "notice": "Notice", "needToLoginAgain": "You need to login again to effect update", "deleteAccountSuccess": "Account deleted successfully. Your session has been expired.", "pleaseAllowAccessCameraGallery": "Please allow access to the camera and gallery", "failToAssign": "Failed to assign User", "goBack": "Go back", "privacyTerms": "Privacy & Terms", "results": "Results", "danish": "Danish", "getNotified": "Get notified!", "imIn": "I'm in", "allowCameraAccess": "Allow Camera access?", "weNeedCameraAccessTo": "We need camera access to scan for QR code or Bar code.", "noCameraPermissionIsGranted": "No camera permission is granted. Please grant it in your device's Settings.", "generatingLink": "Generating link...", "invalidYearOfBirth": "Invalid Year of Birth", "failedToGenerateLink": "Failed to generate link", "codExtraFee": "COD Extra Fee", "moreFromStore": "More from {store}", "store": "Store", "changedCurrencyTo": "Changed currency to {currency}", "appearance": "Appearance", "lightTheme": "Light Theme", "theFieldIsRequired": "The {fieldName} field is required", "youHaveBeenSaveAddressYourLocal": "You have saved address to your local file successful !", "driverAssigned": "Driver Assigned", "enterYourPhone": "Enter your phone number to get started.", "otpVerification": "OTP Verification", "weSentAnOTPTo": "An authentication code has been send to", "emptyShippingMsg": "There are no shipping options available. Please ensure that your address has been entered correctly, or contact us if you need any help.", "canNotUpdateInfo": "Can not update user info.", "canNotCreateUser": "Can not create the user.", "accountIsPendingApproval": "The account is pending approval.", "userNameInCorrect": "The username or password is incorrect.", "canNotSaveOrder": "Can't save the order to website", "tagNotExist": "This tag does not exist", "canNotGetToken": "Can not get token Info.", "canNotGetShipping": "Can not get shipping methods", "canNotGetPayments": "Can not get payment methods", "canNotCreateOrder": "Can not create order", "titleAToZ": "Title: A to Z", "titleZToA": "Title: Z to A", "popularity": "Popularity", "averageRating": "Average Rating", "tooManyFaildedLogin": "Too many failed login attempts. Please try again later.", "viewOnGoogleMaps": "View on Google Maps", "smsCodeExpired": "The SMS code has expired. Please re-send the verification code to try again.", "youNeedToLoginCheckout": "You need to login to checkout", "productReview": "Product Review", "activeNow": "Active now", "activeLongAgo": "Active a long time ago", "activeFor": "Active {x}", "typing": "Typing...", "isTyping": "is typing...", "sessionExpired": "Session Expired", "noConversation": "No conversation yet", "noConversationDescription": "It will appear once your customers start chatting with you", "menuOrder": "Menu order", "transactionFailded": "Transaction failed", "doesNotSupportApplePay": "Apple Pay is not available on this device!", "currencyIsNotSupported": "{currency} is not supported", "retry": "Retry", "failedToLoadAppConfig": "Failed to load application configuration. Please try again or restart your application.", "yourAccountIsUnderReview": "Your account is under review. Please contact the administrator if you need any help.", "newAppConfig": "New content available!", "seeNewAppConfig": "Continue to see new content on your app.", "orderTracking": "Order tracking", "lockScreenAndSecurity": "Lock screen and security", "fingerprintsTouchID": "Fingerprints, Touch ID", "enableForCheckout": "Enable for Checkout", "enableForWallet": "Enable for Wallet", "enableForLogin": "Enable for Login", "ourBankDetails": "Our bank details", "bank": "Bank", "accountNumber": "Account Number", "sortCode": "Sort Code", "registerAs": "Register as", "vendor": "<PERSON><PERSON><PERSON>", "customer": "Customer", "chatGPT": "Chat GPT", "regenerateResponse": "Regenerate response", "clearConversation": "Clear conversation", "listening": "Listening...", "typeAMessage": "Type a message...", "speechNotAvailable": "Speech not available", "tapTheMicToTalk": "Tap the mic to talk", "chatWithBot": "<PERSON><PERSON> with <PERSON><PERSON>", "imageGenerate": "Image generate", "confirmDelete": "Are you sure you want to delete this? This action cannot be undone.", "copy": "Copy", "copied": "<PERSON>pied", "showMore": "Show More", "showLess": "Show Less", "registerAsVendor": "Register As Vendor", "selectRole": "Select Role", "changeLanguage": "Change language", "whichLanguageDoYouPrefer": "Which language do you prefer?", "callToVendor": "Call to Store Owner", "sendSMStoVendor": "Send SMS to Store Owner", "paymentMethodIsNotSupported": "This payment method is not supported", "welcomeUser": "Welcome {name}", "minAmountForPayment": "The minimum amount for using this payment is {currency} {amount}", "maxAmountForPayment": "The maximum amount for using this payment is {currency} {amount}", "productOutOfStock": "This product is out of stock", "quantityProductExceedInStock": "The current quantity is more than the quantity in stock", "manageApiKey": "Manage API Key", "selectStore": "Select Store", "convertPoint": "{priceRate} = {pointRate} Points", "useMaximumPointDiscount": "Use maximum {maxPointDiscount} Points for a {maxPriceDiscount} discount on this order!", "noPermissionForCurrentRole": "Sorry, this product is not accessible for your current role.", "productExpired": "Sorry, this product cannot be accessed as it has expired.", "noPermissionToViewProduct": "This product is available for users with specific roles.", "noPermissionToViewProductMsg": "Please log in with the appropriate credentials to access this product or contact us for more information.", "featureNotAvailable": "Feature not available", "wholesaleRegisterMsg": "Please reach out to the administrator to approve your registration.", "notFindResult": "Sorry, we couldn't find any results.", "canNotPlayVideo": "Sorry, this video cannot be played.", "becomeAVendor": "Become a Vendor", "countryIsNotSupported": "{country} country is not supported", "payWithAmount": "Pay {amount}", "nameOnCard": "Name On Card", "setup": "Set up", "thisIsCustomerRole": "This is customer role", "thisIsVendorRole": "This is vendor role", "by": "by", "promptPayID": "PromptPay ID:", "promptPayName": "PromptPay Name:", "promptPayType": "PromptPay Type:", "noVideoFound": "Sorry, no videos found.", "mustBeBoughtInGroupsOf": "must be bought in groups of {number}", "pleaseIncreaseOrDecreaseTheQuantity": "Please increase or decrease the quantity to continue.", "searchByCountryNameOrDialCode": "Search by country name or dial code", "openingHours": "Opening Hours", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "closed": "Closed", "open24Hours": "Open 24h", "byAppointmentOnly": "By Appointment Only", "saveQRCode": "Save QR Code", "qRCodeMsgSuccess": "QR code has been saved successfully.", "qRCodeSaveFailure": "Failed to Save QR Code", "productNameCanNotEmpty": "Product name can not be empty", "quantity": "Quantity", "price": "Price", "addToQuoteRequest": "Add to quote request", "informationTable": "Information Table", "to": "To", "oneEachRecipient": "1 to each recipient", "separateMultipleEmailWithComma": "Separate multiple email addresses with a comma.", "enterEmailEachRecipient": "Enter an email address for each recipient", "addMessage": "Add a message", "optional": "optional", "confirmPassword": "Confirm password", "branch": "Branch", "chooseBranch": "Choose the branch", "pleaseChooseBranch": "Please choose a branch", "characterRemain": "{number} characters remaining", "copyright": "© 2024 Idea2App All rights reserved.", "passwordIsRequired": "The Password field is required", "confirmPasswordIsRequired": "The Confirm password field is required", "passwordsDoNotMatch": "Passwords do not match", "emptyBookingHistoryMsg": "Looks like you haven't made any bookings yet. \nStart exploring and make your first booking!", "productAddToCart": "{name} have been added to cart successfully", "notRated": "Not rated", "itemQuantity": "{count, plural, one {{count} item} other {{count} items}}", "yourOrderIsEmpty": "Your orders is empty", "yourOrderIsEmptyMsg": "Looks like you haven't added any items.\nStart shopping to fill it in.", "createReviewSuccess": "Thank you for your review", "createReviewSuccessMsg": "We truly appreciate your input and value your contribution in helping us improve", "areYouWantToExit": "Are you sure you want to exit?", "doYouWantToLeaveWithoutSubmit": "Do you want to leave without submitting your review?", "rateProduct": "Rate Product", "uploadImage": "Upload image", "viewDetail": "View detail", "view": "View", "terrible": "Terrible", "poor": "Poor", "fair": "Fair", "good": "Good", "amazing": "Amazing", "refunds": "refunds", "welcomeBack": "Welcome back", "welcomeRegister": "Start your shopping journey with us now", "viewCart": "View cart", "order": "Order", "youHaveNewOrder": "You have a new order", "orderCancelled": "Order cancelled", "hasBeenCanceled": "Has been canceled", "yourOrderNumber": "Your order number #{orderNumber}", "cancelOrder": "Cancel order", "areYouSureCancelOrder": "Are you sure you want to cancel this order?", "canceled": "Canceled", "refunded": "Refunded", "delivery": "Delivering", "failed": "Failed", "attach": "Attach", "pleaseAttachYourPaymentScreenshot": "Please attach your payment screenshot", "pay": "Pay", "copiedToClipboard": "Copied to clipboard", "paymentScreenshot": "Payment Screenshot", "pleaseUploadYourPaymentScreenshot": "Please upload your payment screenshot", "pleaseChooseValidCity": "Please choose a valid city", "alreadyHaveAnAccount": "Already have an account?", "thisWebsiteIsNotActive": "This website is not active", "thisAppIsNotActive": "This app is not active", "chooseTemplate": "<PERSON><PERSON>", "locationPicked": "Location Picked", "changeTemplate": "Change Template", "previous": "Previous", "getYourApp": "Get Your App", "selectLocation": "Select Location", "locationOutsideBoundary": "Location is outside the boundary", "locationSelectedSuccessfully": "Location selected successfully", "pickLocationFromMap": "Pick location from map", "pleasePickYourLocationFromMap": "Please pick your location from the map", "storeClosedPleaseTryAgainLater": "Store is closed. Please try again later !", "deliveryRoute": "Delivery Route", "pleasePickYourDeliveryDateTime": "Please pick your delivery date and time", "pickTime": "Pick Time", "reservationDetails": "Reservation Details", "numberOfPeople": "Number of People", "nameRequired": "Name is required", "phoneRequired": "Phone number is required", "reservationSuccess": "Reservation successful", "reservationFailed": "Reservation failed", "fillAllFields": "Please fill all fields", "reserveNow": "Reserve Now", "enterDeliveryRouteDetails": "Enter delivery route details", "deliveryRouteRequired": "Delivery route is required", "fromTime": "From Time", "toTime": "To Time", "pickDate": "Pick Date", "pleasePickYourDeliveryDate": "Please pick your delivery date", "pleasePickFromTime": "Please pick from time", "pleasePickToTime": "Please pick to time"}