// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(x) => "نشط ${x}";

  static String m1(attribute) => "أي ${attribute}";

  static String m2(point) => "نقاطك المتاحة: ${point}";

  static String m3(state) => "محول البلوتوث هو ${state}";

  static String m4(currency) => "تم تغيير العملة إلى ${currency}";

  static String m5(number) => " متبقي${number} حرف";

  static String m6(priceRate, pointRate) => "${priceRate} = ${pointRate} نقطة";

  static String m7(count) => "${count} عنصر";

  static String m8(count) => "${count} من العناصر";

  static String m9(country) => " البلد${country} غير مدعوم";

  static String m10(currency) => " ${currency} غير مدعوم";

  static String m11(day) => "${day} أيام  ماضية";

  static String m12(total) => "~${total} كم";

  static String m13(timeLeft) => "ينتهي في ${timeLeft}";

  static String m14(captcha) => "أدخل ${captcha} للتأكيد:";

  static String m15(message) => "خطأ: ${message}";

  static String m16(time) => "تنتهي الصلاحية في ${time}";

  static String m17(total) => ">${total} كم";

  static String m18(hour) => "${hour} ساعات ماضية";

  static String m19(count) =>
      " ${Intl.plural(count, one: '${count} item', other: '${count} items')}";

  static String m20(message) =>
      "حدثت مشكلة في التطبيق أثناء طلب البيانات ، يرجى الاتصال بالمسؤول لإصلاح المشكلة: ${message}";

  static String m21(currency, amount) =>
      "الحد الأقصى لمبلغ استخدام هذه الدفعة هو ${currency} ${amount}";

  static String m22(size) => "أقصى حجم للملف: ${size} ميغابايت";

  static String m23(currency, amount) =>
      "الحد الأدنى لمبلغ استخدام هذه الدفعة هو ${currency} ${amount}";

  static String m24(minute) => "${minute} دقائق ماضية";

  static String m25(month) => " قبل${month} شهر";

  static String m26(store) => "المزيد من ${store}";

  static String m27(number) => "يجب شراؤها في مجموعات من ${number}";

  static String m28(itemCount) => "${itemCount} عنصر";

  static String m29(price) => "إجمالي الخيارات: ${price}";

  static String m30(amount) => "ادفع ${amount}";

  static String m31(name) => " تمت إضافة${name} إلى سلة التسوق بنجاح";

  static String m32(total) => "الكمية: ${total}";

  static String m33(percent) => "خصم ${percent} ٪";

  static String m34(keyword) => "نتائج البحث عن: \'${keyword}\'";

  static String m35(keyword, count) => "${keyword} (عنصر واحد (${count} )";

  static String m36(keyword, count) => "${keyword} (${count} عنصر)";

  static String m37(second) => "${second} ثواني ماضية";

  static String m38(totalCartQuantity) =>
      "سلة التسوق, تحتوي ${totalCartQuantity} عنصر";

  static String m39(numberOfUnitsSold) => "مباع: ${numberOfUnitsSold}";

  static String m40(fieldName) => "الحقل ${fieldName} مطلوب";

  static String m41(total) => "السعر الكلي: ${total}";

  static String m42(total) => "عدد المنتجات: ${total}";

  static String m43(total) => "${total} من المنتجات";

  static String m44(total) => "${total} من المنتجات";

  static String m45(maxPointDiscount, maxPriceDiscount) =>
      "استخدم ${maxPointDiscount} نقطة كحد أقصى للحصول على خصم ${maxPriceDiscount} على هذا الطلب!";

  static String m46(date) => "صالح حتى ${date}";

  static String m47(message) => "تحذير: ${message}";

  static String m48(defaultCurrency) =>
      "العملة المحددة حالياً غير متاحة لميزة المحفظة ، الرجاء تغييرها إلى ${defaultCurrency}";

  static String m49(length) => "تم العثور على${length} منتجاً";

  static String m50(week) => "الأسبوع ${week}";

  static String m51(name) => "مرحبًا ${name}";

  static String m52(year) => " قبل${year} سنة";

  static String m53(total) => "لقد قمت بتعيين الطلب رقم${total}";

  static String m54(orderNumber) => "رقم طلبك #${orderNumber}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("معلومات عنا"),
    "account": MessageLookupByLibrary.simpleMessage("الحساب"),
    "accountDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "يؤدي حذف حسابك إلى إزالة المعلومات الشخصية من قاعدة بياناتنا. يصبح بريدك الإلكتروني محجوزاً بشكل دائم ولا يمكن إعادة استخدام نفس البريد الإلكتروني لتسجيل حساب جديد.",
    ),
    "accountIsPendingApproval": MessageLookupByLibrary.simpleMessage(
      "الحساب في انتظار الموافقة.",
    ),
    "accountNumber": MessageLookupByLibrary.simpleMessage("رقم حساب"),
    "accountSetup": MessageLookupByLibrary.simpleMessage("اعدادات الحساب"),
    "active": MessageLookupByLibrary.simpleMessage("نشيط"),
    "activeFor": m0,
    "activeLongAgo": MessageLookupByLibrary.simpleMessage("نشط منذ وقت طويل"),
    "activeNow": MessageLookupByLibrary.simpleMessage("نشط الآن"),
    "addAName": MessageLookupByLibrary.simpleMessage("أضف اسماً"),
    "addANewPost": MessageLookupByLibrary.simpleMessage("أضف منشور جديد"),
    "addASlug": MessageLookupByLibrary.simpleMessage("أضف صلاق"),
    "addAnAttr": MessageLookupByLibrary.simpleMessage("أضف سمة"),
    "addListing": MessageLookupByLibrary.simpleMessage("إضافة قائمة"),
    "addMessage": MessageLookupByLibrary.simpleMessage("اضف رسالة"),
    "addNew": MessageLookupByLibrary.simpleMessage("اضف جديد"),
    "addNewAddress": MessageLookupByLibrary.simpleMessage("إضافة عنوان جديد"),
    "addNewBlog": MessageLookupByLibrary.simpleMessage("أضف مدونة جديدة"),
    "addNewPost": MessageLookupByLibrary.simpleMessage("إنشاء منشور جديد"),
    "addProduct": MessageLookupByLibrary.simpleMessage("أضف منتج"),
    "addToCart": MessageLookupByLibrary.simpleMessage("أضف إلى السلة"),
    "addToCartMaximum": MessageLookupByLibrary.simpleMessage(
      "تم تجاوز الكمية القصوى",
    ),
    "addToCartSucessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة إلى سلة التسوق بنجاح",
    ),
    "addToOrder": MessageLookupByLibrary.simpleMessage("أضف للطلب"),
    "addToQuoteRequest": MessageLookupByLibrary.simpleMessage(
      "إضافة إلى طلب الاقتباس",
    ),
    "addToWishlist": MessageLookupByLibrary.simpleMessage(
      "أضف إلى قائمة الامنيات",
    ),
    "added": MessageLookupByLibrary.simpleMessage("تمت الإضافة"),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة بنجاح",
    ),
    "addingYourImage": MessageLookupByLibrary.simpleMessage("إضافة صورتك"),
    "additionalInformation": MessageLookupByLibrary.simpleMessage(
      "معلومات إضافية",
    ),
    "additionalServices": MessageLookupByLibrary.simpleMessage("خدمات إضافية"),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "adults": MessageLookupByLibrary.simpleMessage("الكبار"),
    "afternoon": MessageLookupByLibrary.simpleMessage("بعد الظهر"),
    "agree": MessageLookupByLibrary.simpleMessage("موافقة"),
    "agreeWithPrivacy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "albanian": MessageLookupByLibrary.simpleMessage("الألبانية"),
    "all": MessageLookupByLibrary.simpleMessage("الكل"),
    "allBrands": MessageLookupByLibrary.simpleMessage("جميع الماركات"),
    "allDeliveryOrders": MessageLookupByLibrary.simpleMessage("جميع الطلبات"),
    "allOrders": MessageLookupByLibrary.simpleMessage("أحدث المبيعات"),
    "allProducts": MessageLookupByLibrary.simpleMessage("جميع المنتجات"),
    "allow": MessageLookupByLibrary.simpleMessage("السماح"),
    "allowCameraAccess": MessageLookupByLibrary.simpleMessage(
      "السماح بالوصول إلى الكاميرا؟",
    ),
    "almostSoldOut": MessageLookupByLibrary.simpleMessage(
      "تم بيع كل الكمية تقريباً",
    ),
    "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "هل لديك حساب؟",
    ),
    "amazing": MessageLookupByLibrary.simpleMessage("مدهش"),
    "amount": MessageLookupByLibrary.simpleMessage("كمية"),
    "anyAttr": m1,
    "appearance": MessageLookupByLibrary.simpleMessage("نوع العرض"),
    "apply": MessageLookupByLibrary.simpleMessage("تطبيق"),
    "approve": MessageLookupByLibrary.simpleMessage("يوافق"),
    "approved": MessageLookupByLibrary.simpleMessage("وافق"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("هل أنت متأكد ؟"),
    "areYouSureCancelOrder": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد إلغاء هذا الطلب؟",
    ),
    "areYouSureDeleteAccount": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف حسابك؟",
    ),
    "areYouWantToExit": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد الخروج؟",
    ),
    "assigned": MessageLookupByLibrary.simpleMessage("تعيين"),
    "atLeastThreeCharacters": MessageLookupByLibrary.simpleMessage(
      "3 أحرف على الأقل ...",
    ),
    "attach": MessageLookupByLibrary.simpleMessage("إرفاق"),
    "attributeAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "السمة موجودة بالفعل",
    ),
    "attributes": MessageLookupByLibrary.simpleMessage("المواصفات"),
    "audioDetected": MessageLookupByLibrary.simpleMessage(
      "تم الكشف عن عنصر (عناصر) الصوت. هل تريد إضافة مشغل الصوت؟",
    ),
    "availability": MessageLookupByLibrary.simpleMessage("المتوفر"),
    "availablePoints": m2,
    "averageRating": MessageLookupByLibrary.simpleMessage("متوسط التقييم"),
    "back": MessageLookupByLibrary.simpleMessage("الى الخلف"),
    "backOrder": MessageLookupByLibrary.simpleMessage("تحت الطلب"),
    "backToShop": MessageLookupByLibrary.simpleMessage("العودة للمتجر"),
    "backToWallet": MessageLookupByLibrary.simpleMessage("رجوع إلى Wallet"),
    "bagsCollections": MessageLookupByLibrary.simpleMessage("مجموعات الحقائب"),
    "balance": MessageLookupByLibrary.simpleMessage("الرصيد"),
    "bank": MessageLookupByLibrary.simpleMessage("بنك"),
    "bannerListType": MessageLookupByLibrary.simpleMessage("نوع قائمة البانر"),
    "bannerType": MessageLookupByLibrary.simpleMessage("نوع البانر"),
    "bannerYoutubeURL": MessageLookupByLibrary.simpleMessage(
      "عنوان URL للبانر يوتيوب",
    ),
    "basicInformation": MessageLookupByLibrary.simpleMessage("معلومات اساسية"),
    "becomeAVendor": MessageLookupByLibrary.simpleMessage(
      "افتح متجرك في سوق البلد",
    ),
    "bengali": MessageLookupByLibrary.simpleMessage("بنغالي"),
    "billingAddress": MessageLookupByLibrary.simpleMessage(
      "عنوان وصول الفواتير",
    ),
    "bleHasNotBeenEnabled": MessageLookupByLibrary.simpleMessage(
      "لم يتم تمكين البلوتوث",
    ),
    "bleState": m3,
    "blog": MessageLookupByLibrary.simpleMessage("المدونة"),
    "booked": MessageLookupByLibrary.simpleMessage("حجزت بالفعل"),
    "booking": MessageLookupByLibrary.simpleMessage("الحجز"),
    "bookingCancelled": MessageLookupByLibrary.simpleMessage("تم إلغاء الحجز"),
    "bookingConfirm": MessageLookupByLibrary.simpleMessage("تم تأكيد"),
    "bookingError": MessageLookupByLibrary.simpleMessage(
      "هناك شيء ما خاطئ. الرجاء معاودة المحاولة في وقت لاحق.",
    ),
    "bookingHistory": MessageLookupByLibrary.simpleMessage("تاريخ الحجز"),
    "bookingNow": MessageLookupByLibrary.simpleMessage("احجز الآن"),
    "bookingSuccess": MessageLookupByLibrary.simpleMessage("تم الحجز بنجاح"),
    "bookingSummary": MessageLookupByLibrary.simpleMessage("ملخص الحجز"),
    "bookingUnavailable": MessageLookupByLibrary.simpleMessage(
      "الحجز غير متوفر",
    ),
    "bosnian": MessageLookupByLibrary.simpleMessage("البوسنية"),
    "branch": MessageLookupByLibrary.simpleMessage("فرع"),
    "brand": MessageLookupByLibrary.simpleMessage("الماركة"),
    "brazil": MessageLookupByLibrary.simpleMessage("البرازيل"),
    "burmese": MessageLookupByLibrary.simpleMessage("البورمية"),
    "buyNow": MessageLookupByLibrary.simpleMessage("اشتري الأن"),
    "by": MessageLookupByLibrary.simpleMessage("بواسطة"),
    "byAppointmentOnly": MessageLookupByLibrary.simpleMessage(
      "عن طريق موعد فقط",
    ),
    "byCategory": MessageLookupByLibrary.simpleMessage("حسب القسم"),
    "byPrice": MessageLookupByLibrary.simpleMessage("حسب السعر"),
    "bySignup": MessageLookupByLibrary.simpleMessage(
      "بالتسجيل ، فإنك توافق على",
    ),
    "byTag": MessageLookupByLibrary.simpleMessage("بواسطة الوسم"),
    "call": MessageLookupByLibrary.simpleMessage("مكالمة"),
    "callTo": MessageLookupByLibrary.simpleMessage("إجراء مكالمة إلى"),
    "callToVendor": MessageLookupByLibrary.simpleMessage("اتصل بصاحب المتجر"),
    "canNotCreateOrder": MessageLookupByLibrary.simpleMessage(
      "لا يمكنك انشاء الطلب",
    ),
    "canNotCreateUser": MessageLookupByLibrary.simpleMessage(
      "لا يمكن إنشاء المستخدم.",
    ),
    "canNotGetPayments": MessageLookupByLibrary.simpleMessage(
      "لا يمكن الحصول على طرق الدفع",
    ),
    "canNotGetShipping": MessageLookupByLibrary.simpleMessage(
      "لا يمكن الحصول على طرق الشحن",
    ),
    "canNotGetToken": MessageLookupByLibrary.simpleMessage(
      "لا يمكن الحصول على معلومات رمزية.",
    ),
    "canNotLaunch": MessageLookupByLibrary.simpleMessage(
      "لا يمكن تشغيل هذا التطبيق ، تأكد من صحة إعداداتك على ",
    ),
    "canNotLoadThisLink": MessageLookupByLibrary.simpleMessage(
      "لا يمكن تحميل هذا الرابط",
    ),
    "canNotPlayVideo": MessageLookupByLibrary.simpleMessage(
      "معذرة هذا الفيديو لا يمكن تشغيله.",
    ),
    "canNotSaveOrder": MessageLookupByLibrary.simpleMessage(
      "لا يمكن حفظ الطلب في الموقع",
    ),
    "canNotUpdateInfo": MessageLookupByLibrary.simpleMessage(
      "لا يمكن تحديث معلومات المستخدم.",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cancelOrder": MessageLookupByLibrary.simpleMessage("إلغاء الطلب"),
    "canceled": MessageLookupByLibrary.simpleMessage("تم الإلغاء"),
    "cantFindThisOrderId": MessageLookupByLibrary.simpleMessage(
      "لا يمكن العثور على معرّف الطلب هذا",
    ),
    "cantPickDateInThePast": MessageLookupByLibrary.simpleMessage(
      "التاريخ في الماضي غير مسموح به",
    ),
    "cardHolder": MessageLookupByLibrary.simpleMessage("حامل البطاقة"),
    "cardNumber": MessageLookupByLibrary.simpleMessage("رقم البطاقة"),
    "cart": MessageLookupByLibrary.simpleMessage("سلة التسوق"),
    "cartDiscount": MessageLookupByLibrary.simpleMessage("خصم على السلة"),
    "cash": MessageLookupByLibrary.simpleMessage("نقداً"),
    "categories": MessageLookupByLibrary.simpleMessage("الأقسام"),
    "category": MessageLookupByLibrary.simpleMessage("الاقسام"),
    "change": MessageLookupByLibrary.simpleMessage("تغيير"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("غير اللغة"),
    "changePrinter": MessageLookupByLibrary.simpleMessage("تغيير الطابعة"),
    "changeTemplate": MessageLookupByLibrary.simpleMessage("تغيير التصميم"),
    "changedCurrencyTo": m4,
    "characterRemain": m5,
    "chat": MessageLookupByLibrary.simpleMessage("دردشة"),
    "chatGPT": MessageLookupByLibrary.simpleMessage("دردشة GPT"),
    "chatListScreen": MessageLookupByLibrary.simpleMessage(
      "شاشة قائمة الدردشة",
    ),
    "chatViaFacebook": MessageLookupByLibrary.simpleMessage(
      "الدردشة عبر مسنجر",
    ),
    "chatViaWhatApp": MessageLookupByLibrary.simpleMessage(
      "الدردشة عبر واتساب",
    ),
    "chatWithBot": MessageLookupByLibrary.simpleMessage("الدردشة مع بوت"),
    "chatWithStoreOwner": MessageLookupByLibrary.simpleMessage(
      "الدردشة مع المتجر",
    ),
    "checkConfirmLink": MessageLookupByLibrary.simpleMessage(
      "تحقق من بريدك الإلكتروني للحصول على رابط التأكيد",
    ),
    "checking": MessageLookupByLibrary.simpleMessage("تدقيق..."),
    "checkout": MessageLookupByLibrary.simpleMessage("الدفع"),
    "chinese": MessageLookupByLibrary.simpleMessage("الصينية"),
    "chineseSimplified": MessageLookupByLibrary.simpleMessage(
      "الصينية المبسطة)",
    ),
    "chineseTraditional": MessageLookupByLibrary.simpleMessage("تقاليد صينية)"),
    "chooseBranch": MessageLookupByLibrary.simpleMessage("اختر الفرع"),
    "chooseCategory": MessageLookupByLibrary.simpleMessage("اختر القسم"),
    "chooseFromGallery": MessageLookupByLibrary.simpleMessage("اختر من المعرض"),
    "chooseFromServer": MessageLookupByLibrary.simpleMessage("اختر من الخادم"),
    "choosePlan": MessageLookupByLibrary.simpleMessage("اختر خطة"),
    "chooseStaff": MessageLookupByLibrary.simpleMessage("اختر طاقم العمل"),
    "chooseTemplate": MessageLookupByLibrary.simpleMessage("اختر التصميم"),
    "chooseType": MessageLookupByLibrary.simpleMessage("اختر النوع"),
    "chooseYourPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "اختر طريقة الدفع",
    ),
    "city": MessageLookupByLibrary.simpleMessage("المنطقة"),
    "cityIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة المدينة / البلدة مطلوبة",
    ),
    "clear": MessageLookupByLibrary.simpleMessage("مسح"),
    "clearCart": MessageLookupByLibrary.simpleMessage("مسح السلة"),
    "clearConversation": MessageLookupByLibrary.simpleMessage("مسح المحادثة"),
    "close": MessageLookupByLibrary.simpleMessage("أغلق"),
    "closeNow": MessageLookupByLibrary.simpleMessage("مغلق الآن"),
    "closed": MessageLookupByLibrary.simpleMessage("مغلق"),
    "codExtraFee": MessageLookupByLibrary.simpleMessage(
      "الرسوم الإضافية عند الدفع عند الاستلام",
    ),
    "color": MessageLookupByLibrary.simpleMessage("اللون"),
    "comment": MessageLookupByLibrary.simpleMessage("تعليق"),
    "commentFirst": MessageLookupByLibrary.simpleMessage("من فضلك اكتب تعليقك"),
    "commentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "التعليق بنجاح ، يرجى الانتظار حتى تتم الموافقة على تعليقك",
    ),
    "complete": MessageLookupByLibrary.simpleMessage("مكتمل"),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmAccountDeletion": MessageLookupByLibrary.simpleMessage(
      "تأكيد حذف الحساب",
    ),
    "confirmClearCartWhenTopUp": MessageLookupByLibrary.simpleMessage(
      "سيتم مسح السلة عند التوصيل.",
    ),
    "confirmClearTheCart": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد مسح سلة التسوق؟",
    ),
    "confirmDelete": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا؟ لا يمكن التراجع عن هذا الإجراء.",
    ),
    "confirmDeleteItem": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك ترغب في حذف هذا العنصر؟",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "confirmPasswordIsRequired": MessageLookupByLibrary.simpleMessage(
      "حقل تأكيد كلمة المرور مطلوب",
    ),
    "confirmRemoveProductInCart": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد إزالة هذا المنتج؟",
    ),
    "confirmed": MessageLookupByLibrary.simpleMessage("تم التأكيد"),
    "connect": MessageLookupByLibrary.simpleMessage("الاتصال"),
    "contact": MessageLookupByLibrary.simpleMessage("اتصل"),
    "content": MessageLookupByLibrary.simpleMessage("يحتوى"),
    "continueShopping": MessageLookupByLibrary.simpleMessage("مواصلة التسوق"),
    "continueToPayment": MessageLookupByLibrary.simpleMessage("أدفع الأن"),
    "continueToReview": MessageLookupByLibrary.simpleMessage("مراجعة الطلب"),
    "continueToShipping": MessageLookupByLibrary.simpleMessage("تفاصيل الشحن"),
    "continues": MessageLookupByLibrary.simpleMessage("استمر"),
    "conversations": MessageLookupByLibrary.simpleMessage("المحادثات"),
    "convertPoint": m6,
    "copied": MessageLookupByLibrary.simpleMessage("نسخ"),
    "copiedToClipboard": MessageLookupByLibrary.simpleMessage(
      "تم النسخ إلى الحافظة",
    ),
    "copy": MessageLookupByLibrary.simpleMessage("نسخ"),
    "copyright": MessageLookupByLibrary.simpleMessage(
      "© 2024 Idea2App جميع الحقوق محفوظة.",
    ),
    "countItem": m7,
    "countItems": m8,
    "country": MessageLookupByLibrary.simpleMessage("البلد"),
    "countryIsNotSupported": m9,
    "countryIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة الدولة مطلوبة",
    ),
    "couponCode": MessageLookupByLibrary.simpleMessage("رمز القسيمة"),
    "couponHasBeenSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حفظ القسيمة بنجاح.",
    ),
    "couponInvalid": MessageLookupByLibrary.simpleMessage(
      "رمز القسيمة الخاص بك غير صالح",
    ),
    "couponMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "تهانينا! تم تطبيق رمز القسيمة بنجاح",
    ),
    "createAnAccount": MessageLookupByLibrary.simpleMessage("انشئ حساب"),
    "createNewPostSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء رسالتك بنجاح كمسودة. يرجى إلقاء نظرة على موقع المسؤول الخاص بك.",
    ),
    "createPost": MessageLookupByLibrary.simpleMessage("إنشاء ممشاركةة"),
    "createProduct": MessageLookupByLibrary.simpleMessage("إضافة منتج"),
    "createReviewSuccess": MessageLookupByLibrary.simpleMessage(
      "شكرا لك، لمراجعتك",
    ),
    "createReviewSuccessMsg": MessageLookupByLibrary.simpleMessage(
      "نحن نقدر حقًا مدخلاتك ونقدر مساهمتك في مساعدتنا على التحسن",
    ),
    "createVariants": MessageLookupByLibrary.simpleMessage(
      "إنشاء جميع التخصيصات",
    ),
    "createdOn": MessageLookupByLibrary.simpleMessage("تم الإنشاء في"),
    "currencies": MessageLookupByLibrary.simpleMessage("العملات"),
    "currencyIsNotSupported": m10,
    "currentPassword": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور الحالية",
    ),
    "currentlyWeOnlyHave": MessageLookupByLibrary.simpleMessage(
      "حالياً ، لدينا فقط",
    ),
    "customer": MessageLookupByLibrary.simpleMessage("زبون"),
    "customerDetail": MessageLookupByLibrary.simpleMessage("تفاصيل العميل"),
    "customerNote": MessageLookupByLibrary.simpleMessage("ملاحظة العملاء"),
    "cvv": MessageLookupByLibrary.simpleMessage("CVV"),
    "czech": MessageLookupByLibrary.simpleMessage("تشيكي"),
    "danish": MessageLookupByLibrary.simpleMessage("دانماركي"),
    "darkTheme": MessageLookupByLibrary.simpleMessage("الوضع الداكن"),
    "dashboard": MessageLookupByLibrary.simpleMessage("لوحة التحكم"),
    "dataEmpty": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "dateASC": MessageLookupByLibrary.simpleMessage("تصاعدي التاريخ"),
    "dateBooking": MessageLookupByLibrary.simpleMessage("تاريخ الحجز"),
    "dateDESC": MessageLookupByLibrary.simpleMessage("التاريخ تنازلي"),
    "dateEnd": MessageLookupByLibrary.simpleMessage("تاريخ النهاية"),
    "dateLatest": MessageLookupByLibrary.simpleMessage("التاريخ: الأحدث"),
    "dateOldest": MessageLookupByLibrary.simpleMessage("التاريخ: الأقدم"),
    "dateStart": MessageLookupByLibrary.simpleMessage("تاريخ البدء"),
    "dateTime": MessageLookupByLibrary.simpleMessage("التاريخ والوقت"),
    "dateWiseClose": MessageLookupByLibrary.simpleMessage("تاريخ الإغلاق"),
    "daysAgo": m11,
    "debit": MessageLookupByLibrary.simpleMessage("سحب"),
    "decline": MessageLookupByLibrary.simpleMessage("رفض"),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
    "deleteAccountMsg": MessageLookupByLibrary.simpleMessage(
      "هل انت متأكد انك تريد حذف حسابك؟ يرجى قراءة كيف سيؤثر حذف الحساب.",
    ),
    "deleteAccountSuccess": MessageLookupByLibrary.simpleMessage(
      " انتهت صلاحية هذا الاتصال",
    ),
    "deleteAll": MessageLookupByLibrary.simpleMessage("حذف الكل"),
    "delivered": MessageLookupByLibrary.simpleMessage("تم التوصيل"),
    "deliveredTo": MessageLookupByLibrary.simpleMessage("سلمت لـ"),
    "delivering": MessageLookupByLibrary.simpleMessage("جاري توصيل"),
    "delivery": MessageLookupByLibrary.simpleMessage("جاري التوصيل"),
    "deliveryBoy": MessageLookupByLibrary.simpleMessage("مندوب التوصيل:"),
    "deliveryDate": MessageLookupByLibrary.simpleMessage("تاريخ التسليم"),
    "deliveryDetails": MessageLookupByLibrary.simpleMessage("تفاصيل التسليم"),
    "deliveryManagement": MessageLookupByLibrary.simpleMessage("توصيل"),
    "deliveryNotificationError": MessageLookupByLibrary.simpleMessage(
      "لايوجد بيانات.\nتمت إزالة هذا الطلب.",
    ),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "descriptionEnterVoucher": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال أو تحديد قسيمة طلبك.",
    ),
    "didntReceiveCode": MessageLookupByLibrary.simpleMessage(
      "لم استلم رمز التأكيد؟",
    ),
    "direction": MessageLookupByLibrary.simpleMessage("اتجاه"),
    "disablePurchase": MessageLookupByLibrary.simpleMessage("تعطيل الشراء"),
    "discount": MessageLookupByLibrary.simpleMessage("خصم"),
    "displayName": MessageLookupByLibrary.simpleMessage("اسم العرض"),
    "distance": m12,
    "doNotAnyTransactions": MessageLookupByLibrary.simpleMessage(
      "ليس لديك أي معاملات حتى الآن",
    ),
    "doYouWantToExitApp": MessageLookupByLibrary.simpleMessage(
      "هل تريد الخروج من التطبيق",
    ),
    "doYouWantToLeaveWithoutSubmit": MessageLookupByLibrary.simpleMessage(
      "هل تريد المغادرة دون تقديم رأيك؟",
    ),
    "doYouWantToLogout": MessageLookupByLibrary.simpleMessage("هل ترغب بخروج؟"),
    "doesNotSupportApplePay": MessageLookupByLibrary.simpleMessage(
      "ApplePay غير مدعوم. يرجى التحقق من محفظتك وبطاقتك",
    ),
    "done": MessageLookupByLibrary.simpleMessage("تم"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب ؟"),
    "download": MessageLookupByLibrary.simpleMessage("تحميل"),
    "downloadApp": MessageLookupByLibrary.simpleMessage("تحميل التطبيق"),
    "draft": MessageLookupByLibrary.simpleMessage("مسودة"),
    "driverAssigned": MessageLookupByLibrary.simpleMessage("سائق معين"),
    "duration": MessageLookupByLibrary.simpleMessage("المدة"),
    "dutch": MessageLookupByLibrary.simpleMessage("هولندي"),
    "earnings": MessageLookupByLibrary.simpleMessage("أرباح"),
    "edit": MessageLookupByLibrary.simpleMessage("تعديل:"),
    "editProductInfo": MessageLookupByLibrary.simpleMessage(
      "تحرير معلومات المنتج",
    ),
    "editWithoutColon": MessageLookupByLibrary.simpleMessage("تصحيح"),
    "egypt": MessageLookupByLibrary.simpleMessage("مصر"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإكتروني"),
    "emailDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "سيؤدي حذف حسابك إلى إلغاء اشتراكك من جميع القوائم البريدية.",
    ),
    "emailDoesNotExist": MessageLookupByLibrary.simpleMessage(
      "حساب البريد الإكتروني الذي أدخلته غير موجود. حاول مرة اخرى.",
    ),
    "emailIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة البريد الإكتروني مطلوبة",
    ),
    "emailSubscription": MessageLookupByLibrary.simpleMessage(
      "اشتراك البريد الإلكتروني",
    ),
    "emptyBookingHistoryMsg": MessageLookupByLibrary.simpleMessage(
      "يبدو أنك لم تقم بأي حجوزات بعد.\nابدأ الاستكشاف وقم بالحجز الأول!",
    ),
    "emptyCart": MessageLookupByLibrary.simpleMessage("عربة فارغة"),
    "emptyCartSubtitle": MessageLookupByLibrary.simpleMessage(
      "يبدو أنك لم تقم بإضافة أي سلع إلى السلة بعد. ابدأ التسوق الآن.",
    ),
    "emptyCartSubtitle02": MessageLookupByLibrary.simpleMessage(
      "أُووبس! عربة التسوق الخاصة بك تشعر بالضوء قليلا.\n\nعلى استعداد للتسوق لشيء رائع؟",
    ),
    "emptyComment": MessageLookupByLibrary.simpleMessage(
      "تعليقك لا يمكن أن يكون خالياً",
    ),
    "emptySearch": MessageLookupByLibrary.simpleMessage(
      "لم تبحث عن العناصر حتى الآن. لنبدأ الآن - سنساعدك.",
    ),
    "emptyShippingMsg": MessageLookupByLibrary.simpleMessage(
      "يرجى التأكد من تحديد موقعك على الخريطة في الخطوة السابقة، أو الاتصال بنا إذا كنت بحاجة إلى أي مساعدة.",
    ),
    "emptyUsername": MessageLookupByLibrary.simpleMessage(
      "اسم المستخدم / البريد الإكتروني فارغ",
    ),
    "emptyWishlist": MessageLookupByLibrary.simpleMessage("قائمة أمنيات فارغة"),
    "emptyWishlistSubtitle": MessageLookupByLibrary.simpleMessage(
      "اضغط على أيقونة القلب بجوار المنتج لإضافته إلى قائمة منتجاتك المفضلة. سنقوم بحفظها لك هنا!",
    ),
    "emptyWishlistSubtitle02": MessageLookupByLibrary.simpleMessage(
      "قائمة أمنياتك فارغة حاليًا.\nابدأ بإضافة المنتجات الآن!",
    ),
    "enableForCheckout": MessageLookupByLibrary.simpleMessage("تفعيل للدفع"),
    "enableForLogin": MessageLookupByLibrary.simpleMessage(
      "تفعيل لتسجيل الدخول",
    ),
    "enableForWallet": MessageLookupByLibrary.simpleMessage("تفعيل للمحفظة"),
    "enableVacationMode": MessageLookupByLibrary.simpleMessage(
      "تفعيل وضع الإجازة",
    ),
    "endDateCantBeAfterFirstDate": MessageLookupByLibrary.simpleMessage(
      "الرجاء تحديد تاريخ بعد التاريخ الأول",
    ),
    "endsIn": m13,
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "enterCaptcha": m14,
    "enterEmailEachRecipient": MessageLookupByLibrary.simpleMessage(
      "أدخل عنوان البريد الإلكتروني لكل مستلم",
    ),
    "enterSendedCode": MessageLookupByLibrary.simpleMessage(
      "أدخل الرمز المرسل إليه",
    ),
    "enterVoucherCode": MessageLookupByLibrary.simpleMessage(
      "أدخل رمز القسيمة",
    ),
    "enterYourEmail": MessageLookupByLibrary.simpleMessage(
      "أدخل بريدك الإلكتروني",
    ),
    "enterYourEmailOrUsername": MessageLookupByLibrary.simpleMessage(
      "أدخل بريدك الإلكتروني أو اسم المستخدم",
    ),
    "enterYourFirstName": MessageLookupByLibrary.simpleMessage("أدخل اسمك"),
    "enterYourLastName": MessageLookupByLibrary.simpleMessage("أدخل لقبك"),
    "enterYourMobile": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال رقم هاتفك المحمول",
    ),
    "enterYourPassword": MessageLookupByLibrary.simpleMessage("ادخل كلمة السر"),
    "enterYourPhone": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتفك للبدء.",
    ),
    "enterYourPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتفك",
    ),
    "error": m15,
    "errorAmountTransfer": MessageLookupByLibrary.simpleMessage(
      "المبلغ الذي تم إدخاله أكبر من مبلغ المحفظة الحالي. حاول مرة اخرى!",
    ),
    "errorEmailFormat": MessageLookupByLibrary.simpleMessage(
      "من فضلك أدخل بريد الكترونى صحيح.",
    ),
    "errorPasswordFormat": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال كلمة مرور لا تقل عن 8 أحرف",
    ),
    "evening": MessageLookupByLibrary.simpleMessage("مساء"),
    "events": MessageLookupByLibrary.simpleMessage("أحداث"),
    "expectedDeliveryDate": MessageLookupByLibrary.simpleMessage(
      "تاريخ التسليم المتوقع",
    ),
    "expired": MessageLookupByLibrary.simpleMessage("منتهية الصلاحية"),
    "expiredDate": MessageLookupByLibrary.simpleMessage("منتهي الصلاحية"),
    "expiredDateHint": MessageLookupByLibrary.simpleMessage("شهر / سنة"),
    "expiringInTime": m16,
    "exploreNow": MessageLookupByLibrary.simpleMessage("استكشف الآن"),
    "external": MessageLookupByLibrary.simpleMessage("خارجي"),
    "extraServices": MessageLookupByLibrary.simpleMessage("خدمات إضافية"),
    "failToAssign": MessageLookupByLibrary.simpleMessage("فشل تعيين المستخدم"),
    "failed": MessageLookupByLibrary.simpleMessage("فشل"),
    "failedToGenerateLink": MessageLookupByLibrary.simpleMessage(
      "فشل إنشاء الارتباط",
    ),
    "failedToLoadAppConfig": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل التطبيق. يرجى إعادة تشغيل التطبيق.",
    ),
    "failedToLoadImage": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل الصورة",
    ),
    "fair": MessageLookupByLibrary.simpleMessage("معرض"),
    "favorite": MessageLookupByLibrary.simpleMessage("مفضل"),
    "featureNotAvailable": MessageLookupByLibrary.simpleMessage(
      "الميزة غير متوفرة",
    ),
    "featureProducts": MessageLookupByLibrary.simpleMessage("منتجات مميزة"),
    "featured": MessageLookupByLibrary.simpleMessage("متميز"),
    "features": MessageLookupByLibrary.simpleMessage("المميزات"),
    "fileIsTooBig": MessageLookupByLibrary.simpleMessage(
      "الملف كبير جدًا. الرجاء اختيار ملف أصغر!",
    ),
    "fileUploadFailed": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل الملف!",
    ),
    "files": MessageLookupByLibrary.simpleMessage("ملفات"),
    "filter": MessageLookupByLibrary.simpleMessage("تخصيص"),
    "fingerprintsTouchID": MessageLookupByLibrary.simpleMessage(
      "بصمات الأصابع ، معرف اللمس",
    ),
    "finishSetup": MessageLookupByLibrary.simpleMessage("إنهاء الإعدادات"),
    "finnish": MessageLookupByLibrary.simpleMessage("اللغة الفنلندية"),
    "firstComment": MessageLookupByLibrary.simpleMessage(
      "كن أول من يعلق على هذا المنشور!",
    ),
    "firstName": MessageLookupByLibrary.simpleMessage("الاسم"),
    "firstNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة الاسم مطلوبة",
    ),
    "firstRenewal": MessageLookupByLibrary.simpleMessage("التجديد الأول"),
    "fixedCartDiscount": MessageLookupByLibrary.simpleMessage(
      "خصم ثابت على السلة",
    ),
    "fixedProductDiscount": MessageLookupByLibrary.simpleMessage(
      "خصم ثابت على المنتج ",
    ),
    "forThisProduct": MessageLookupByLibrary.simpleMessage("لأجل هذا المنتج"),
    "freeOfCharge": MessageLookupByLibrary.simpleMessage("مجاني"),
    "french": MessageLookupByLibrary.simpleMessage("فرنسي"),
    "friday": MessageLookupByLibrary.simpleMessage("يوم الجمعة"),
    "from": MessageLookupByLibrary.simpleMessage("من"),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "gallery": MessageLookupByLibrary.simpleMessage("صالة عرض"),
    "generalSetting": MessageLookupByLibrary.simpleMessage("الإعدادات العامة"),
    "generatingLink": MessageLookupByLibrary.simpleMessage(
      "جاري إنشاء الارتباط ...",
    ),
    "german": MessageLookupByLibrary.simpleMessage("ألماني"),
    "getNotification": MessageLookupByLibrary.simpleMessage(
      "الحصول على إشعارات",
    ),
    "getNotified": MessageLookupByLibrary.simpleMessage(
      " الحصول على التنبيهات!",
    ),
    "getPasswordLink": MessageLookupByLibrary.simpleMessage(
      "الحصول على رابط كلمة المرور",
    ),
    "getStarted": MessageLookupByLibrary.simpleMessage("البدء"),
    "getYourApp": MessageLookupByLibrary.simpleMessage("احصل على تطبيقك"),
    "goBack": MessageLookupByLibrary.simpleMessage("رجوع"),
    "goBackHomePage": MessageLookupByLibrary.simpleMessage(
      "قم بالعودة إلى الصفحة الرئيسية",
    ),
    "goBackToAddress": MessageLookupByLibrary.simpleMessage("العودة للعنوان"),
    "goBackToReview": MessageLookupByLibrary.simpleMessage("العودة للتفاصيل"),
    "goBackToShipping": MessageLookupByLibrary.simpleMessage("العودة للشحن"),
    "good": MessageLookupByLibrary.simpleMessage("حسن"),
    "greaterDistance": m17,
    "greek": MessageLookupByLibrary.simpleMessage("اليونانية"),
    "grossSales": MessageLookupByLibrary.simpleMessage("إجمالي المبيعات"),
    "grouped": MessageLookupByLibrary.simpleMessage("مجمعة"),
    "guests": MessageLookupByLibrary.simpleMessage("ضيوف"),
    "hasBeenCanceled": MessageLookupByLibrary.simpleMessage("تم إلغاء الطلب"),
    "hasBeenDeleted": MessageLookupByLibrary.simpleMessage("تم حذف"),
    "hebrew": MessageLookupByLibrary.simpleMessage("Hebrew"),
    "hideAbout": MessageLookupByLibrary.simpleMessage("اخفاء عن"),
    "hideAddress": MessageLookupByLibrary.simpleMessage("اخفاء العنوان"),
    "hideEmail": MessageLookupByLibrary.simpleMessage("اخفاء البريد الإكتروني"),
    "hideMap": MessageLookupByLibrary.simpleMessage("إخفاء الموقع"),
    "hidePhone": MessageLookupByLibrary.simpleMessage("إخفاء الهاتف"),
    "hidePolicy": MessageLookupByLibrary.simpleMessage("إخفاء السياسة"),
    "hindi": MessageLookupByLibrary.simpleMessage("الهندية"),
    "history": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "historyTransaction": MessageLookupByLibrary.simpleMessage(
      "تاريخ المعاملات",
    ),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "hour": MessageLookupByLibrary.simpleMessage("ساعة"),
    "hoursAgo": m18,
    "hungarian": MessageLookupByLibrary.simpleMessage("المجري"),
    "hungary": MessageLookupByLibrary.simpleMessage("هناقاري"),
    "iAgree": MessageLookupByLibrary.simpleMessage("أنا موافق على "),
    "imIn": MessageLookupByLibrary.simpleMessage("موافق"),
    "imageFeature": MessageLookupByLibrary.simpleMessage("ميزة الصورة"),
    "imageGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
    "imageGenerate": MessageLookupByLibrary.simpleMessage("توليد الصور"),
    "imageNetwork": MessageLookupByLibrary.simpleMessage("الشبكة متعددة الصور"),
    "inStock": MessageLookupByLibrary.simpleMessage("متوفر"),
    "incorrectPassword": MessageLookupByLibrary.simpleMessage("كلمة سر خاطئة"),
    "india": MessageLookupByLibrary.simpleMessage("الهند"),
    "indonesian": MessageLookupByLibrary.simpleMessage("الإندونيسية"),
    "informationTable": MessageLookupByLibrary.simpleMessage("جدول المعلومات"),
    "instantlyClose": MessageLookupByLibrary.simpleMessage("إغلاق على الفور"),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الهاتف غير صحيح",
    ),
    "invalidSMSCode": MessageLookupByLibrary.simpleMessage(
      "رمز تحقق الرسالة النصية غير صالح",
    ),
    "invalidYearOfBirth": MessageLookupByLibrary.simpleMessage(
      "سنة الميلاد غير صحيحة",
    ),
    "invoice": MessageLookupByLibrary.simpleMessage("فاتورة"),
    "isEverythingSet": MessageLookupByLibrary.simpleMessage("هل كل شيء مُعد؟"),
    "isTyping": MessageLookupByLibrary.simpleMessage("يكتب..."),
    "italian": MessageLookupByLibrary.simpleMessage("إيطالي"),
    "item": MessageLookupByLibrary.simpleMessage("عنصر"),
    "itemQuantity": m19,
    "itemTotal": MessageLookupByLibrary.simpleMessage("مجموع:"),
    "items": MessageLookupByLibrary.simpleMessage("العناصر"),
    "itsOrdered": MessageLookupByLibrary.simpleMessage("تم الطلب"),
    "iwantToCreateAccount": MessageLookupByLibrary.simpleMessage(
      "اريد انشاء حساب",
    ),
    "japanese": MessageLookupByLibrary.simpleMessage("اليابانية"),
    "kannada": MessageLookupByLibrary.simpleMessage("الكانادا"),
    "keep": MessageLookupByLibrary.simpleMessage("رجوع"),
    "khmer": MessageLookupByLibrary.simpleMessage("الخمير"),
    "korean": MessageLookupByLibrary.simpleMessage("الكورية"),
    "kurdish": MessageLookupByLibrary.simpleMessage("Kurdish"),
    "language": MessageLookupByLibrary.simpleMessage("تعديل اللغة"),
    "languageSuccess": MessageLookupByLibrary.simpleMessage(
      "تم تحديث اللغة بنجاح",
    ),
    "lao": MessageLookupByLibrary.simpleMessage("لاو"),
    "lastName": MessageLookupByLibrary.simpleMessage("الأسم الأخير"),
    "lastNameIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة الأسم الأخير مطلوبة",
    ),
    "lastTransactions": MessageLookupByLibrary.simpleMessage("آخر المعاملات"),
    "latestProducts": MessageLookupByLibrary.simpleMessage("أحدث المنتجات"),
    "layout": MessageLookupByLibrary.simpleMessage("الأشكال"),
    "lightTheme": MessageLookupByLibrary.simpleMessage("الوضع الفاتح"),
    "link": MessageLookupByLibrary.simpleMessage("الرابط"),
    "listBannerType": MessageLookupByLibrary.simpleMessage("نوع بانر القائمة"),
    "listBannerVideo": MessageLookupByLibrary.simpleMessage("قائمة فيديو بانر"),
    "listMessages": MessageLookupByLibrary.simpleMessage("قائمة الإشعارات"),
    "listening": MessageLookupByLibrary.simpleMessage("الاستماع ..."),
    "loadFail": MessageLookupByLibrary.simpleMessage(
      "فشل التحميل! انقر فوق إعادة المحاولة!",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("تحميل"),
    "loadingLink": MessageLookupByLibrary.simpleMessage("تحميل الرابط ..."),
    "location": MessageLookupByLibrary.simpleMessage("موقعك"),
    "locationOutsideBoundary": MessageLookupByLibrary.simpleMessage(
      "الموقع خارج الحدود",
    ),
    "locationPicked": MessageLookupByLibrary.simpleMessage("تم اختيار الموقع"),
    "locationSelectedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديد الموقع بنجاح",
    ),
    "lockScreenAndSecurity": MessageLookupByLibrary.simpleMessage(
      "شاشة القفل والأمان",
    ),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginCanceled": MessageLookupByLibrary.simpleMessage(
      "تم إلغاء تسجيل الدخول",
    ),
    "loginErrorServiceProvider": m20,
    "loginFailed": MessageLookupByLibrary.simpleMessage("فشل تسجيل الدخول!"),
    "loginInvalid": MessageLookupByLibrary.simpleMessage(
      "لا يسمح لك باستخدام هذا التطبيق.",
    ),
    "loginSuccess": MessageLookupByLibrary.simpleMessage(
      "تم تسجيل الدخول بنجاح",
    ),
    "loginToComment": MessageLookupByLibrary.simpleMessage(
      "الرجاء تسجيل الدخول للتعليق",
    ),
    "loginToContinue": MessageLookupByLibrary.simpleMessage(
      "الرجاء تسجيل الدخول للمتابعة",
    ),
    "loginToYourAccount": MessageLookupByLibrary.simpleMessage(
      "تسجيل الدخول إلى حسابك",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("خروج"),
    "malay": MessageLookupByLibrary.simpleMessage("لغة الملايو"),
    "manCollections": MessageLookupByLibrary.simpleMessage("مجموعات الرجل"),
    "manageApiKey": MessageLookupByLibrary.simpleMessage("إدارة مفتاح API"),
    "manageStock": MessageLookupByLibrary.simpleMessage("إدارة المخزون"),
    "map": MessageLookupByLibrary.simpleMessage("خريطة"),
    "marathi": MessageLookupByLibrary.simpleMessage("المهاراتية"),
    "markAsRead": MessageLookupByLibrary.simpleMessage("ضع إشارة مقروء"),
    "markAsShipped": MessageLookupByLibrary.simpleMessage(
      "اضف علامة - تم التوصيل",
    ),
    "markAsUnread": MessageLookupByLibrary.simpleMessage(
      "وضع علامة كغير مقروء",
    ),
    "maxAmountForPayment": m21,
    "maximumFileSizeMb": m22,
    "maybeLater": MessageLookupByLibrary.simpleMessage("بالإمكان لاحقاً"),
    "menuOrder": MessageLookupByLibrary.simpleMessage("ترتيب القائمة"),
    "menus": MessageLookupByLibrary.simpleMessage("القوائم"),
    "message": MessageLookupByLibrary.simpleMessage("رسالة"),
    "messageTo": MessageLookupByLibrary.simpleMessage("إرسال رسالة إلى"),
    "minAmountForPayment": m23,
    "minimumQuantityIs": MessageLookupByLibrary.simpleMessage(
      "الكمية الأدنى هي",
    ),
    "minutesAgo": m24,
    "mobileVerification": MessageLookupByLibrary.simpleMessage(
      "التحقق من الهاتف المحمول",
    ),
    "momentAgo": MessageLookupByLibrary.simpleMessage("منذ لحظة"),
    "monday": MessageLookupByLibrary.simpleMessage("الإثنين"),
    "monthsAgo": m25,
    "more": MessageLookupByLibrary.simpleMessage("...أكثر"),
    "moreFromStore": m26,
    "moreInformation": MessageLookupByLibrary.simpleMessage("معلومات اكثر"),
    "morning": MessageLookupByLibrary.simpleMessage("صباح"),
    "mustBeBoughtInGroupsOf": m27,
    "mustSelectOneItem": MessageLookupByLibrary.simpleMessage(
      "يجب تحديد عنصر واحد",
    ),
    "myCart": MessageLookupByLibrary.simpleMessage("سلة التسوق"),
    "myOrder": MessageLookupByLibrary.simpleMessage("طلباتي"),
    "myPoints": MessageLookupByLibrary.simpleMessage("نقاطي"),
    "myProducts": MessageLookupByLibrary.simpleMessage("منتجاتي"),
    "myProductsEmpty": MessageLookupByLibrary.simpleMessage(
      "ليس لديك أي منتجات!",
    ),
    "myWallet": MessageLookupByLibrary.simpleMessage("محفظتى"),
    "myWishList": MessageLookupByLibrary.simpleMessage("المفضلة"),
    "nItems": m28,
    "name": MessageLookupByLibrary.simpleMessage("اسم"),
    "nameOnCard": MessageLookupByLibrary.simpleMessage("الاسم على البطاقة"),
    "nearbyPlaces": MessageLookupByLibrary.simpleMessage("الأماكن القريبة"),
    "needToLoginAgain": MessageLookupByLibrary.simpleMessage(
      "تحتاج إلى تسجيل الدخول مرة أخرى لتفعيل التحديث",
    ),
    "netherlands": MessageLookupByLibrary.simpleMessage("هولندي"),
    "newAppConfig": MessageLookupByLibrary.simpleMessage("محتوى جديد متاح!"),
    "newPassword": MessageLookupByLibrary.simpleMessage("كلمة مرور جديدة"),
    "newVariation": MessageLookupByLibrary.simpleMessage("تخصيص جديد"),
    "next": MessageLookupByLibrary.simpleMessage("التالى"),
    "niceName": MessageLookupByLibrary.simpleMessage("الكنية"),
    "no": MessageLookupByLibrary.simpleMessage("لا"),
    "noAddressHaveBeenSaved": MessageLookupByLibrary.simpleMessage(
      "لم يتم حفظ أي عناوين حتى الآن.",
    ),
    "noBackHistoryItem": MessageLookupByLibrary.simpleMessage(
      "لا توجد عناصر محفوظة",
    ),
    "noBlog": MessageLookupByLibrary.simpleMessage(
      "عذراً ، يبدو أن المدونة لم تعد موجودة",
    ),
    "noCameraPermissionIsGranted": MessageLookupByLibrary.simpleMessage(
      "لم يتم منح إذن الكاميرا. يرجى منحه في إعدادات جهازك.",
    ),
    "noConversation": MessageLookupByLibrary.simpleMessage(
      "لا محادثة حتى الآن",
    ),
    "noConversationDescription": MessageLookupByLibrary.simpleMessage(
      "سيظهر بمجرد أن يبدأ عملاؤك في الدردشة معك",
    ),
    "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noFavoritesYet": MessageLookupByLibrary.simpleMessage(
      "قائمة المفضلة فارغة حالياً.",
    ),
    "noFileToDownload": MessageLookupByLibrary.simpleMessage(
      "لا يوجد ملف للتنزيل.",
    ),
    "noForwardHistoryItem": MessageLookupByLibrary.simpleMessage(
      "لاتوجد عناصر محفوظة مرسلة ",
    ),
    "noInternetConnection": MessageLookupByLibrary.simpleMessage(
      "لا يوجد اتصال بالإنترنت",
    ),
    "noListingNearby": MessageLookupByLibrary.simpleMessage(
      "لا توجد متاجر قريبة!",
    ),
    "noOrders": MessageLookupByLibrary.simpleMessage("لا توجد طلبات"),
    "noPermissionForCurrentRole": MessageLookupByLibrary.simpleMessage(
      "عذرًا، لا يمكن الوصول إلى هذا المنتج لدورك الحالي.",
    ),
    "noPermissionToViewProduct": MessageLookupByLibrary.simpleMessage(
      "هذا المنتج متاح للمستخدمين ذوي الأدوار المحددة. يرجى تسجيل الدخول باستخدام بيانات الاعتماد المناسبة للوصول إلى هذا المنتج أو الاتصال بنا للحصول على مزيد من المعلومات.",
    ),
    "noPermissionToViewProductMsg": MessageLookupByLibrary.simpleMessage(
      "يرجى تسجيل الدخول باستخدام بيانات الاعتماد المناسبة للوصول إلى هذا المنتج أو الاتصال بنا للحصول على مزيد من المعلومات.",
    ),
    "noPost": MessageLookupByLibrary.simpleMessage(
      "عذراً ، يبدو أن هذه الصفحة لم تعد موجودة!",
    ),
    "noPrinters": MessageLookupByLibrary.simpleMessage("لا توجد طابعات"),
    "noProduct": MessageLookupByLibrary.simpleMessage("لا يوجد منتجات"),
    "noResultFound": MessageLookupByLibrary.simpleMessage("لاتوجد نتائج"),
    "noReviews": MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
    "noSlotAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد منصة متاحة",
    ),
    "noStoreNearby": MessageLookupByLibrary.simpleMessage("لا يوجد متجر قريب!"),
    "noThanks": MessageLookupByLibrary.simpleMessage("لا شكراً"),
    "noVideoFound": MessageLookupByLibrary.simpleMessage(
      "عذرا، لم يتم العثور على أشرطة الفيديو.",
    ),
    "none": MessageLookupByLibrary.simpleMessage("لا شيء"),
    "notFindResult": MessageLookupByLibrary.simpleMessage(
      "عذرا ، لم نتمكن من العثور على أي نتائج.",
    ),
    "notFound": MessageLookupByLibrary.simpleMessage("غير موجودة"),
    "notRated": MessageLookupByLibrary.simpleMessage("غير مصنف"),
    "note": MessageLookupByLibrary.simpleMessage("ملاحظات"),
    "noteMessage": MessageLookupByLibrary.simpleMessage("ملحوظة"),
    "noteTransfer": MessageLookupByLibrary.simpleMessage("ملاحظة (اختياري)"),
    "notice": MessageLookupByLibrary.simpleMessage("تنويه"),
    "notifications": MessageLookupByLibrary.simpleMessage("إشعارات"),
    "notifyLatestOffer": MessageLookupByLibrary.simpleMessage(
      "أبلغني عن أحدث العروض وتوافر المنتجات",
    ),
    "ofThisProduct": MessageLookupByLibrary.simpleMessage("من هذا المنتج"),
    "off": MessageLookupByLibrary.simpleMessage("خصم"),
    "ok": MessageLookupByLibrary.simpleMessage("حسناً"),
    "on": MessageLookupByLibrary.simpleMessage("على"),
    "onSale": MessageLookupByLibrary.simpleMessage("خصم"),
    "onVacation": MessageLookupByLibrary.simpleMessage("في عطلة"),
    "oneEachRecipient": MessageLookupByLibrary.simpleMessage("1 لكل مستلم"),
    "online": MessageLookupByLibrary.simpleMessage("عبر الانترنت"),
    "open24Hours": MessageLookupByLibrary.simpleMessage("مفتوح 24 ساعة"),
    "openMap": MessageLookupByLibrary.simpleMessage("افتح الخريطة"),
    "openNow": MessageLookupByLibrary.simpleMessage("مفتوح الان"),
    "openingHours": MessageLookupByLibrary.simpleMessage("ساعات العمل"),
    "optional": MessageLookupByLibrary.simpleMessage("اختياري"),
    "options": MessageLookupByLibrary.simpleMessage("خيارات"),
    "optionsTotal": m29,
    "or": MessageLookupByLibrary.simpleMessage("أو"),
    "orLoginWith": MessageLookupByLibrary.simpleMessage(
      "أو تسجيل الدخول باستخدام",
    ),
    "order": MessageLookupByLibrary.simpleMessage("طلب"),
    "orderCancelled": MessageLookupByLibrary.simpleMessage("تم إلغاء الطلب"),
    "orderConfirmation": MessageLookupByLibrary.simpleMessage("تأكيد الطلب"),
    "orderConfirmationMsg": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من إنشاء الطلب؟",
    ),
    "orderDate": MessageLookupByLibrary.simpleMessage("تاريخ الطلب"),
    "orderDetail": MessageLookupByLibrary.simpleMessage("تفاصيل الطلب"),
    "orderHistory": MessageLookupByLibrary.simpleMessage("سجل الطلبات"),
    "orderId": MessageLookupByLibrary.simpleMessage("رقم الطلب:"),
    "orderIdWithoutColon": MessageLookupByLibrary.simpleMessage("رقم الطلب"),
    "orderNo": MessageLookupByLibrary.simpleMessage("رقم الطلب"),
    "orderNotes": MessageLookupByLibrary.simpleMessage("ملاحظات الطلب"),
    "orderNumber": MessageLookupByLibrary.simpleMessage("رقم الطلب"),
    "orderStatusCanceledReversal": MessageLookupByLibrary.simpleMessage(
      "رفض الإلغاء",
    ),
    "orderStatusCancelled": MessageLookupByLibrary.simpleMessage("ملغية"),
    "orderStatusChargeBack": MessageLookupByLibrary.simpleMessage(
      "إعادة التوصيل",
    ),
    "orderStatusCompleted": MessageLookupByLibrary.simpleMessage("مكتملة"),
    "orderStatusDenied": MessageLookupByLibrary.simpleMessage("مرفوض"),
    "orderStatusExpired": MessageLookupByLibrary.simpleMessage(
      "منتهي الصلاحية",
    ),
    "orderStatusFailed": MessageLookupByLibrary.simpleMessage("فشلت"),
    "orderStatusOnHold": MessageLookupByLibrary.simpleMessage("في الانتظار"),
    "orderStatusPending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
    "orderStatusPendingPayment": MessageLookupByLibrary.simpleMessage(
      "في انتظار الدفع",
    ),
    "orderStatusProcessed": MessageLookupByLibrary.simpleMessage(
      "تمت المعالجة",
    ),
    "orderStatusProcessing": MessageLookupByLibrary.simpleMessage(
      "قيد المعالجة",
    ),
    "orderStatusRefunded": MessageLookupByLibrary.simpleMessage("مرتجعة"),
    "orderStatusReversed": MessageLookupByLibrary.simpleMessage("تم إسترجاعه"),
    "orderStatusShipped": MessageLookupByLibrary.simpleMessage("تم شحنه"),
    "orderStatusVoided": MessageLookupByLibrary.simpleMessage("باطل"),
    "orderSuccessMsg1": MessageLookupByLibrary.simpleMessage(
      "يمكنك التحقق من حالة طلبك باستخدام ميزة حالة التسليم الخاصة بنا.",
    ),
    "orderSuccessMsg2": MessageLookupByLibrary.simpleMessage(
      "يمكنك تسجيل الدخول إلى حسابك باستخدام البريد الإكتروني وكلمة المرور المحددة مسبقًا. على حسابك ، يمكنك تحرير بيانات ملفك الشخصي ، والتحقق من سجل الطلبات .",
    ),
    "orderSuccessTitle1": MessageLookupByLibrary.simpleMessage(
      "لقد تم الطلب بنجاح",
    ),
    "orderSuccessTitle2": MessageLookupByLibrary.simpleMessage(
      "الحساب الخاص بك",
    ),
    "orderSummary": MessageLookupByLibrary.simpleMessage("ملخص الطلب"),
    "orderTotal": MessageLookupByLibrary.simpleMessage("الطلب الكلي"),
    "orderTracking": MessageLookupByLibrary.simpleMessage("تتبع الطلب"),
    "orders": MessageLookupByLibrary.simpleMessage("الطلبات"),
    "otpVerification": MessageLookupByLibrary.simpleMessage("التحقق من OTP"),
    "ourBankDetails": MessageLookupByLibrary.simpleMessage(
      "التفاصيل المصرفية الخاصة بنا",
    ),
    "outOfStock": MessageLookupByLibrary.simpleMessage("سيتوفر قريباً"),
    "pageView": MessageLookupByLibrary.simpleMessage("عرض الصفحة"),
    "paid": MessageLookupByLibrary.simpleMessage("دفع"),
    "paidStatus": MessageLookupByLibrary.simpleMessage("مدفوع"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة السر"),
    "passwordIsRequired": MessageLookupByLibrary.simpleMessage(
      "حقل كلمة المرور مطلوب",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور غير مطابقة",
    ),
    "pasteYourImageUrl": MessageLookupByLibrary.simpleMessage(
      "إلصق عنوان الرابط الخاص بالصورة",
    ),
    "pay": MessageLookupByLibrary.simpleMessage("دفع"),
    "payByWallet": MessageLookupByLibrary.simpleMessage(
      "الدفع عن طريق المحفظة",
    ),
    "payNow": MessageLookupByLibrary.simpleMessage("ادفع الآن"),
    "payWithAmount": m30,
    "payment": MessageLookupByLibrary.simpleMessage("الدفع"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("طريقة الدفع"),
    "paymentMethodIsNotSupported": MessageLookupByLibrary.simpleMessage(
      "طريقة الدفع هذه غير مدعومة",
    ),
    "paymentMethods": MessageLookupByLibrary.simpleMessage("طرق الدفع"),
    "paymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "لقطة شاشة الدفع",
    ),
    "paymentSuccessful": MessageLookupByLibrary.simpleMessage("تم الدفع بنجاح"),
    "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
    "persian": MessageLookupByLibrary.simpleMessage("الفارسية"),
    "phone": MessageLookupByLibrary.simpleMessage("هاتف"),
    "phoneEmpty": MessageLookupByLibrary.simpleMessage("الهاتف خالي"),
    "phoneHintFormat": MessageLookupByLibrary.simpleMessage(
      "التنسيق: +213-000-00-00-00",
    ),
    "phoneIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة رقم الهاتف مطلوبة",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "phoneNumberVerification": MessageLookupByLibrary.simpleMessage(
      "رمز تأكيد الرقم",
    ),
    "pickADate": MessageLookupByLibrary.simpleMessage("اختر التاريخ والوقت"),
    "pickLocationFromMap": MessageLookupByLibrary.simpleMessage(
      "اختر الموقع من الخريطة",
    ),
    "placeMyOrder": MessageLookupByLibrary.simpleMessage("تنفيذ الطلب"),
    "playAll": MessageLookupByLibrary.simpleMessage("تشغيل الكل"),
    "pleaseAddPrice": MessageLookupByLibrary.simpleMessage(
      "الرجاء إضافة السعر",
    ),
    "pleaseAgreeTerms": MessageLookupByLibrary.simpleMessage(
      "نرجو أن تقوم بالموافقة على شروطنا",
    ),
    "pleaseAllowAccessCameraGallery": MessageLookupByLibrary.simpleMessage(
      "يرجى السماح بالوصول إلى الكاميرا والمعرض",
    ),
    "pleaseAttachYourPaymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "برجاء إرفاق لقطة شاشة الدفع الخاصة بك",
    ),
    "pleaseCheckInternet": MessageLookupByLibrary.simpleMessage(
      "يرجى التحقق من اتصال الإنترنت!",
    ),
    "pleaseChooseBranch": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار فرع",
    ),
    "pleaseChooseCategory": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار الصنف",
    ),
    "pleaseChooseValidCity": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار مدينة صحيحة",
    ),
    "pleaseEnterProductName": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال اسم المنتج",
    ),
    "pleaseFillCode": MessageLookupByLibrary.simpleMessage(
      "نرجوا أن تقوم بملء الكود الخاص بك",
    ),
    "pleaseIncreaseOrDecreaseTheQuantity": MessageLookupByLibrary.simpleMessage(
      "يرجى زيادة أو تقليل الكمية للإستمرار.",
    ),
    "pleaseInput": MessageLookupByLibrary.simpleMessage("يرجى ملء جميع الحقول"),
    "pleaseInputFillAllFields": MessageLookupByLibrary.simpleMessage(
      "يرجى ملء جميع الخانات",
    ),
    "pleasePickYourLocationFromMap": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار موقعك من الخريطة",
    ),
    "pleaseSelectADate": MessageLookupByLibrary.simpleMessage(
      "الرجاء تحديد تاريخ الحجز",
    ),
    "pleaseSelectALocation": MessageLookupByLibrary.simpleMessage(
      "يرجى تحديد الموقع",
    ),
    "pleaseSelectAllAttributes": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار مواصفات المنتج (مثال: اللون)",
    ),
    "pleaseSelectAttr": MessageLookupByLibrary.simpleMessage(
      "الرجاء تحديد خيار واحد على الأقل لكل سمة نشطة",
    ),
    "pleaseSelectImages": MessageLookupByLibrary.simpleMessage(
      "الرجاء تحديد الصور",
    ),
    "pleaseSelectRequiredOptions": MessageLookupByLibrary.simpleMessage(
      "الرجاء تحديد الخيارات المطلوبة!",
    ),
    "pleaseSignInBeforeUploading": MessageLookupByLibrary.simpleMessage(
      "يرجى تسجيل الدخول إلى حسابك قبل تحميل أي ملفات.",
    ),
    "pleaseUploadYourPaymentScreenshot": MessageLookupByLibrary.simpleMessage(
      "يرجى تحميل لقطة شاشة الدفع الخاصة بك",
    ),
    "pleasefillUpAllCellsProperly": MessageLookupByLibrary.simpleMessage(
      "*يرجى ملء جميع الخانات بشكل صحيح",
    ),
    "point": MessageLookupByLibrary.simpleMessage("نقطة"),
    "pointMsgConfigNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على اعدادات نقاط خصم في الخادم",
    ),
    "pointMsgEnter": MessageLookupByLibrary.simpleMessage(
      "الرجاء إدخال نقطة الخصم",
    ),
    "pointMsgMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لنقطة الخصم",
    ),
    "pointMsgNotEnough": MessageLookupByLibrary.simpleMessage(
      "ليس لديك نقطة خصم كافية. مجموع نقاط الخصم الخاصة بك هو",
    ),
    "pointMsgOverMaximumDiscountPoint": MessageLookupByLibrary.simpleMessage(
      "لقد وصلت إلى أقصى نقطة خصم",
    ),
    "pointMsgOverTotalBill": MessageLookupByLibrary.simpleMessage(
      "إجمالي قيمة الخصم أعلى من إجمالي الفاتورة",
    ),
    "pointMsgRemove": MessageLookupByLibrary.simpleMessage(
      "تمت إزالة نقطة الخصم",
    ),
    "pointMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "تم تطبيق نقطة الخصم بنجاح",
    ),
    "pointRewardMessage": MessageLookupByLibrary.simpleMessage(
      "هناك تخفيض يمكنك تطبيقه من نقاطك على قفتك",
    ),
    "polish": MessageLookupByLibrary.simpleMessage("بولندية"),
    "poor": MessageLookupByLibrary.simpleMessage("فقير"),
    "popular": MessageLookupByLibrary.simpleMessage("متميز"),
    "popularity": MessageLookupByLibrary.simpleMessage("الشعبية"),
    "posAddressToolTip": MessageLookupByLibrary.simpleMessage(
      "سيتم حفظ هذا العنوان على جهازك. إنه ليس عنوان المستخدم.",
    ),
    "postContent": MessageLookupByLibrary.simpleMessage("يحتوى"),
    "postFail": MessageLookupByLibrary.simpleMessage("تم فشل إنشاء رسالتك"),
    "postImageFeature": MessageLookupByLibrary.simpleMessage("ميزة الصورة"),
    "postManagement": MessageLookupByLibrary.simpleMessage("إدارة البريد"),
    "postProduct": MessageLookupByLibrary.simpleMessage("أنشر المنتج"),
    "postSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء رسالتك بنجاح",
    ),
    "postTitle": MessageLookupByLibrary.simpleMessage("عنوان"),
    "prepaid": MessageLookupByLibrary.simpleMessage("مسبقة الدفع"),
    "prev": MessageLookupByLibrary.simpleMessage("السابق"),
    "preview": MessageLookupByLibrary.simpleMessage("معاينة"),
    "previous": MessageLookupByLibrary.simpleMessage("السابق"),
    "price": MessageLookupByLibrary.simpleMessage("السعر"),
    "priceHighToLow": MessageLookupByLibrary.simpleMessage("السعر: تنازلي"),
    "priceLowToHigh": MessageLookupByLibrary.simpleMessage("السعر: تصاعدي"),
    "prices": MessageLookupByLibrary.simpleMessage("الأسعار"),
    "printReceipt": MessageLookupByLibrary.simpleMessage("اطبع الايصال"),
    "printer": MessageLookupByLibrary.simpleMessage("طابعة"),
    "printerNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على الطابعة",
    ),
    "printerSelection": MessageLookupByLibrary.simpleMessage("تحديد الطابعة"),
    "printing": MessageLookupByLibrary.simpleMessage("جاري الطباعة ..."),
    "privacyAndTerm": MessageLookupByLibrary.simpleMessage("الخصوصية والشروط"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "privacyTerms": MessageLookupByLibrary.simpleMessage("الخصوصية والبنود"),
    "private": MessageLookupByLibrary.simpleMessage("خاص"),
    "product": MessageLookupByLibrary.simpleMessage("المنتج"),
    "productAddToCart": m31,
    "productAdded": MessageLookupByLibrary.simpleMessage("تمت إضافة المنتج"),
    "productCreateReview": MessageLookupByLibrary.simpleMessage(
      "سيظهر منتجك بعد الآراء.",
    ),
    "productExpired": MessageLookupByLibrary.simpleMessage(
      "عذرًا، لا يمكن الوصول إلى هذا المنتج نظرًا لانتهاء صلاحيته.",
    ),
    "productName": MessageLookupByLibrary.simpleMessage("اسم المنتج"),
    "productNameCanNotEmpty": MessageLookupByLibrary.simpleMessage(
      "لا يمكن أن يكون اسم المنتج فارغاً",
    ),
    "productNeedAtLeastOneVariation": MessageLookupByLibrary.simpleMessage(
      "يحتاج متغير نوع المنتج إلى متغير واحد على الأقل",
    ),
    "productNeedNameAndPrice": MessageLookupByLibrary.simpleMessage(
      "نوع المنتج بسيط يحتاج إلى الاسم والسعر العادي",
    ),
    "productOutOfStock": MessageLookupByLibrary.simpleMessage(
      "نفد هذا المنتج من المخازن",
    ),
    "productOverview": MessageLookupByLibrary.simpleMessage(
      "نظرة عامة على المنتج",
    ),
    "productRating": MessageLookupByLibrary.simpleMessage("تقييم المنتج"),
    "productReview": MessageLookupByLibrary.simpleMessage("تقييم المنتج"),
    "productType": MessageLookupByLibrary.simpleMessage("نوع المنتج"),
    "products": MessageLookupByLibrary.simpleMessage("المنتجات"),
    "promoCodeNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على رمز العرض الترويجي",
    ),
    "promptPayID": MessageLookupByLibrary.simpleMessage("معرف الدفع الفوري:"),
    "promptPayName": MessageLookupByLibrary.simpleMessage("اسم الدفع الفوري:"),
    "promptPayType": MessageLookupByLibrary.simpleMessage("نوع الدفع الفوري:"),
    "publish": MessageLookupByLibrary.simpleMessage("نشر"),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage(
      "قم بالسحب لتحميل المزيد",
    ),
    "qRCodeMsgSuccess": MessageLookupByLibrary.simpleMessage(
      "تم حفظ رمز الاستجابة السريعة بنجاح.",
    ),
    "qRCodeSaveFailure": MessageLookupByLibrary.simpleMessage(
      "فشل في حفظ رمز الاستجابة السريعة",
    ),
    "qty": MessageLookupByLibrary.simpleMessage("الكمية"),
    "qtyTotal": m32,
    "quantity": MessageLookupByLibrary.simpleMessage("كمية"),
    "quantityProductExceedInStock": MessageLookupByLibrary.simpleMessage(
      "الكمية الحالية أكبر من الكمية الموجودة في المخزون",
    ),
    "rate": MessageLookupByLibrary.simpleMessage("قيّم"),
    "rateProduct": MessageLookupByLibrary.simpleMessage("تقييم المنتج"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage("قم بتقييم التطبيق"),
    "rateThisApp": MessageLookupByLibrary.simpleMessage(
      "قم بتقييم هذا التطبيق",
    ),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "إذا اعجبك التطبيق نرجو منك تقييم التطبيق، هذا بالتأكيد يساعدنا. ولن يأخذ سوى دقيقة من وقتك.",
    ),
    "rating": MessageLookupByLibrary.simpleMessage("تقييم"),
    "ratingFirst": MessageLookupByLibrary.simpleMessage(
      "يرجى التقييم قبل إرسال تعليقك",
    ),
    "reOrder": MessageLookupByLibrary.simpleMessage("إعادة الطلب"),
    "readReviews": MessageLookupByLibrary.simpleMessage("قراءة التعليقات"),
    "receivedMoney": MessageLookupByLibrary.simpleMessage("النقود المستلمة"),
    "receiver": MessageLookupByLibrary.simpleMessage("المتلقي"),
    "recentSearches": MessageLookupByLibrary.simpleMessage(
      "عمليات البحث الأخيرة",
    ),
    "recentView": MessageLookupByLibrary.simpleMessage("آخر المشاهدات"),
    "recentlyViewed": MessageLookupByLibrary.simpleMessage("شوهدت مؤخراً"),
    "recents": MessageLookupByLibrary.simpleMessage("الأحدث"),
    "recommended": MessageLookupByLibrary.simpleMessage("موصى به"),
    "recurringTotals": MessageLookupByLibrary.simpleMessage("الأجمالى المكرر"),
    "refresh": MessageLookupByLibrary.simpleMessage("تحديث"),
    "refund": MessageLookupByLibrary.simpleMessage("إعادة مال"),
    "refundOrderFailed": MessageLookupByLibrary.simpleMessage(
      "لم ينجح طلب استرداد أموال الطلب",
    ),
    "refundOrderSuccess": MessageLookupByLibrary.simpleMessage(
      "اطلب استرداد المبلغ المدفوع لطلبك بنجاح!",
    ),
    "refundRequest": MessageLookupByLibrary.simpleMessage("طلب ارجاع"),
    "refundRequested": MessageLookupByLibrary.simpleMessage("استرداد طلب"),
    "refunded": MessageLookupByLibrary.simpleMessage("تم الإسترداد"),
    "refunds": MessageLookupByLibrary.simpleMessage("المبالغ المستردة"),
    "regenerateResponse": MessageLookupByLibrary.simpleMessage(
      "تجديد الاستجابة",
    ),
    "registerAs": MessageLookupByLibrary.simpleMessage("سجل باسم"),
    "registerAsVendor": MessageLookupByLibrary.simpleMessage("التسجيل كبائع"),
    "registerFailed": MessageLookupByLibrary.simpleMessage("فشل التسجيل"),
    "registerSuccess": MessageLookupByLibrary.simpleMessage("تم التسجيل بنجاح"),
    "regularPrice": MessageLookupByLibrary.simpleMessage("السعر العادي"),
    "relatedLayoutTitle": MessageLookupByLibrary.simpleMessage(
      "أشياء قد تحبها",
    ),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "حرر لتحميل أكثر",
    ),
    "remove": MessageLookupByLibrary.simpleMessage("إزالة"),
    "removeFromWishList": MessageLookupByLibrary.simpleMessage(
      "حذف من قائمة المفضلة",
    ),
    "requestBooking": MessageLookupByLibrary.simpleMessage("طلب الحجز"),
    "requestTooMany": MessageLookupByLibrary.simpleMessage(
      "لقد طلبت عدداً كبيراً جداً من الرموز في وقت قصير. الرجاء معاودة المحاولة في وقت لاحق.",
    ),
    "resend": MessageLookupByLibrary.simpleMessage("أعد الإرسال"),
    "reset": MessageLookupByLibrary.simpleMessage("إعادة تعيين"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة السر",
    ),
    "resetYourPassword": MessageLookupByLibrary.simpleMessage(
      "اعد ضبط كلمه السر",
    ),
    "results": MessageLookupByLibrary.simpleMessage("النتائج"),
    "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
    "review": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "reviewApproval": MessageLookupByLibrary.simpleMessage("مراجعة الآراء"),
    "reviewPendingApproval": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رأيك وينتظر الموافقة!",
    ),
    "reviewSent": MessageLookupByLibrary.simpleMessage("تم إرسال رأيك!"),
    "reviews": MessageLookupByLibrary.simpleMessage("التعليقات"),
    "romanian": MessageLookupByLibrary.simpleMessage("روماني"),
    "russian": MessageLookupByLibrary.simpleMessage("Russian"),
    "sale": m33,
    "salePrice": MessageLookupByLibrary.simpleMessage("سعر الخصم"),
    "saturday": MessageLookupByLibrary.simpleMessage("يوم السبت"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "saveAddress": MessageLookupByLibrary.simpleMessage("احفظ العنوان"),
    "saveAddressSuccess": MessageLookupByLibrary.simpleMessage(
      "تم إدخال عنوانك بنجاح",
    ),
    "saveForLater": MessageLookupByLibrary.simpleMessage("احفظ لوقت لاحق"),
    "saveQRCode": MessageLookupByLibrary.simpleMessage(
      "حفظ رمز الاستجابة السريعة",
    ),
    "saveToWishList": MessageLookupByLibrary.simpleMessage(
      "حفظ في قائمة المفضلة",
    ),
    "scannerCannotScan": MessageLookupByLibrary.simpleMessage(
      "لا يمكن فحص هذا العنصر",
    ),
    "scannerLoginFirst": MessageLookupByLibrary.simpleMessage(
      "لمسح الطلب، تحتاج إلى تسجيل الدخول أولاً",
    ),
    "scannerOrderAvailable": MessageLookupByLibrary.simpleMessage(
      "هذا الطلب غير متاح لحسابك",
    ),
    "search": MessageLookupByLibrary.simpleMessage("البحث"),
    "searchByCountryNameOrDialCode": MessageLookupByLibrary.simpleMessage(
      "البحث حسب اسم البلد أو رمز الاتصال",
    ),
    "searchByName": MessageLookupByLibrary.simpleMessage("البحث بالاسم ..."),
    "searchForItems": MessageLookupByLibrary.simpleMessage("البحث عن منتجات"),
    "searchInput": MessageLookupByLibrary.simpleMessage(
      "يرجى كتابة ماتود البحث عنه في خانة البحث",
    ),
    "searchOrderId": MessageLookupByLibrary.simpleMessage(
      "البحث في معرف الطلب ...",
    ),
    "searchPlace": MessageLookupByLibrary.simpleMessage("مكان البحث"),
    "searchResultFor": m34,
    "searchResultItem": m35,
    "searchResultItems": m36,
    "searchingAddress": MessageLookupByLibrary.simpleMessage(
      "حدد موقعك على الخريطة",
    ),
    "secondsAgo": m37,
    "seeAll": MessageLookupByLibrary.simpleMessage("اظهار الكل"),
    "seeNewAppConfig": MessageLookupByLibrary.simpleMessage(
      "استمر في رؤية المحتوى الجديد على تطبيقك.",
    ),
    "seeOrder": MessageLookupByLibrary.simpleMessage("مشاهدة الطلب"),
    "seeReviews": MessageLookupByLibrary.simpleMessage("شاهد الآراء"),
    "select": MessageLookupByLibrary.simpleMessage("تحديد"),
    "selectAddress": MessageLookupByLibrary.simpleMessage("اختر العنوان"),
    "selectAll": MessageLookupByLibrary.simpleMessage("اختر الكل"),
    "selectDates": MessageLookupByLibrary.simpleMessage("حدد التواريخ"),
    "selectFileCancelled": MessageLookupByLibrary.simpleMessage(
      "تم إلغاء تحديد الملف!",
    ),
    "selectImage": MessageLookupByLibrary.simpleMessage("اختر صورة"),
    "selectLocation": MessageLookupByLibrary.simpleMessage("حدد الموقع"),
    "selectNone": MessageLookupByLibrary.simpleMessage("لا تختر شيء"),
    "selectPrinter": MessageLookupByLibrary.simpleMessage("حدد الطابعة"),
    "selectRole": MessageLookupByLibrary.simpleMessage("اختر الدور"),
    "selectStore": MessageLookupByLibrary.simpleMessage("حدد المتجر"),
    "selectTheColor": MessageLookupByLibrary.simpleMessage("اختر اللون"),
    "selectTheFile": MessageLookupByLibrary.simpleMessage("حدد الملف"),
    "selectThePoint": MessageLookupByLibrary.simpleMessage("اختر النقاط"),
    "selectTheQuantity": MessageLookupByLibrary.simpleMessage("اختر الكمية"),
    "selectTheSize": MessageLookupByLibrary.simpleMessage("اختر المقاس"),
    "selectVoucher": MessageLookupByLibrary.simpleMessage("اختر قسيمة"),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "sendBack": MessageLookupByLibrary.simpleMessage("أرسل للخلف"),
    "sendSMSCode": MessageLookupByLibrary.simpleMessage("احصل على رمز تحقق"),
    "sendSMStoVendor": MessageLookupByLibrary.simpleMessage(
      "إرسال الرسائل القصيرة إلى صاحب المتجر",
    ),
    "separateMultipleEmailWithComma": MessageLookupByLibrary.simpleMessage(
      "افصل بين عناوين البريد الإلكتروني المتعددة بفاصلة.",
    ),
    "serbian": MessageLookupByLibrary.simpleMessage("الصربية"),
    "sessionExpired": MessageLookupByLibrary.simpleMessage("انتهت الجلسة"),
    "setAnAddressInSettingPage": MessageLookupByLibrary.simpleMessage(
      "يرجى تحديد عنوان في صفحة الإعدادات",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "setup": MessageLookupByLibrary.simpleMessage("يثبت"),
    "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
    "shipped": MessageLookupByLibrary.simpleMessage("تم شحنه"),
    "shipping": MessageLookupByLibrary.simpleMessage("التوصيل"),
    "shippingAddress": MessageLookupByLibrary.simpleMessage("عنوان التوصيل"),
    "shippingMethod": MessageLookupByLibrary.simpleMessage("خيارات التوصيل"),
    "shop": MessageLookupByLibrary.simpleMessage("المتجر"),
    "shopEmail": MessageLookupByLibrary.simpleMessage("إيميل المتجر"),
    "shopName": MessageLookupByLibrary.simpleMessage("اسم المتجر"),
    "shopOrders": MessageLookupByLibrary.simpleMessage("طلبات المتجر"),
    "shopPhone": MessageLookupByLibrary.simpleMessage("هاتف المتجر"),
    "shopSlug": MessageLookupByLibrary.simpleMessage("صلاق المتجر"),
    "shoppingCartItems": m38,
    "shortDescription": MessageLookupByLibrary.simpleMessage("وصف قصير"),
    "showAllMyOrdered": MessageLookupByLibrary.simpleMessage("عرض كل طلباتك"),
    "showDetails": MessageLookupByLibrary.simpleMessage("اظهر التفاصيل"),
    "showGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
    "showLess": MessageLookupByLibrary.simpleMessage("عرض أقل"),
    "showMore": MessageLookupByLibrary.simpleMessage("أظهر المزيد"),
    "signIn": MessageLookupByLibrary.simpleMessage("الدخول"),
    "signInWithEmail": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "signUp": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "signup": MessageLookupByLibrary.simpleMessage("حساب جديد"),
    "simple": MessageLookupByLibrary.simpleMessage("بسيط"),
    "size": MessageLookupByLibrary.simpleMessage("المقاس"),
    "skip": MessageLookupByLibrary.simpleMessage("تجاوز"),
    "sku": MessageLookupByLibrary.simpleMessage("SKU"),
    "slovak": MessageLookupByLibrary.simpleMessage("السلوفاكية"),
    "smsCodeExpired": MessageLookupByLibrary.simpleMessage(
      "انتهت صلاحية رمز SMS. يرجى إعادة إرسال رمز التحقق للمحاولة مرة أخرى.",
    ),
    "sold": m39,
    "soldBy": MessageLookupByLibrary.simpleMessage("من طرف"),
    "somethingWrong": MessageLookupByLibrary.simpleMessage(
      "هناك خطأ ما. الرجاء معاودة المحاولة في وقت لاحق.",
    ),
    "sortBy": MessageLookupByLibrary.simpleMessage("ترتيب حسب"),
    "sortCode": MessageLookupByLibrary.simpleMessage("الكود البنكى"),
    "spanish": MessageLookupByLibrary.simpleMessage("الأسبانية"),
    "speechNotAvailable": MessageLookupByLibrary.simpleMessage(
      "الكلام غير متوفر",
    ),
    "startExploring": MessageLookupByLibrary.simpleMessage("ابدأ الاستكشاف"),
    "startShopping": MessageLookupByLibrary.simpleMessage("تصفح المنتجات"),
    "state": MessageLookupByLibrary.simpleMessage("حالة"),
    "stateIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة المحافظة / الولاية / المقاطعة مطلوبة",
    ),
    "stateProvince": MessageLookupByLibrary.simpleMessage("المحافظة"),
    "status": MessageLookupByLibrary.simpleMessage("الحالة"),
    "stock": MessageLookupByLibrary.simpleMessage("المخزون"),
    "stockQuantity": MessageLookupByLibrary.simpleMessage("كمية المخزون"),
    "stop": MessageLookupByLibrary.simpleMessage("متوقف"),
    "store": MessageLookupByLibrary.simpleMessage("متجر"),
    "storeAddress": MessageLookupByLibrary.simpleMessage("عنوان المتجر"),
    "storeBanner": MessageLookupByLibrary.simpleMessage("لافتة المتجر"),
    "storeBrand": MessageLookupByLibrary.simpleMessage("ماركة المتجر"),
    "storeClosed": MessageLookupByLibrary.simpleMessage("المتجر مغلق الآن"),
    "storeClosedPleaseTryAgainLater": MessageLookupByLibrary.simpleMessage(
      "المتجر مغلق. يرجى المحاولة مرة أخرى في وقت لاحق.",
    ),
    "storeEmail": MessageLookupByLibrary.simpleMessage("إيميل المتجر "),
    "storeInformation": MessageLookupByLibrary.simpleMessage("معلومات المتجر"),
    "storeListBanner": MessageLookupByLibrary.simpleMessage(
      "لافتة قائمة المتاجر ",
    ),
    "storeLocation": MessageLookupByLibrary.simpleMessage("مكان المتجر"),
    "storeLogo": MessageLookupByLibrary.simpleMessage("شعار المتجر"),
    "storeMobileBanner": MessageLookupByLibrary.simpleMessage(
      "لافتة الموبايل للمتجر",
    ),
    "storeSettings": MessageLookupByLibrary.simpleMessage("إعدادات المتجر"),
    "storeSliderBanner": MessageLookupByLibrary.simpleMessage(
      "لافتة شريط التمرير للمتجر",
    ),
    "storeStaticBanner": MessageLookupByLibrary.simpleMessage(
      "لافتة ثابتة للمتجر",
    ),
    "storeVacation": MessageLookupByLibrary.simpleMessage("اجازة المتجر"),
    "stores": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "street": MessageLookupByLibrary.simpleMessage("شارع"),
    "street2": MessageLookupByLibrary.simpleMessage("شارع 2"),
    "streetIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة اسم الشارع مطلوبة",
    ),
    "streetName": MessageLookupByLibrary.simpleMessage("اسم الشارع"),
    "streetNameApartment": MessageLookupByLibrary.simpleMessage("الشقة"),
    "streetNameBlock": MessageLookupByLibrary.simpleMessage("المبنى"),
    "subTitleOrderConfirmed": MessageLookupByLibrary.simpleMessage(
      "شكرا لطلبك. نحن نعمل بسرعة لمعالجة طلبك.",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("أرسال"),
    "submitYourPost": MessageLookupByLibrary.simpleMessage("أرسل رسالتك"),
    "subtotal": MessageLookupByLibrary.simpleMessage("المجموع"),
    "sunday": MessageLookupByLibrary.simpleMessage("الأحد"),
    "support": MessageLookupByLibrary.simpleMessage("الدعم"),
    "swahili": MessageLookupByLibrary.simpleMessage("السواحيلية"),
    "swedish": MessageLookupByLibrary.simpleMessage("اللغة السويدية"),
    "tagNotExist": MessageLookupByLibrary.simpleMessage(
      "هذه العلامة غير موجودة",
    ),
    "tags": MessageLookupByLibrary.simpleMessage("العلامات"),
    "takePicture": MessageLookupByLibrary.simpleMessage("التقاط صورة"),
    "tamil": MessageLookupByLibrary.simpleMessage("التاميل"),
    "tapSelectLocation": MessageLookupByLibrary.simpleMessage(
      "قم بالضغظ لاختيار المكان",
    ),
    "tapTheMicToTalk": MessageLookupByLibrary.simpleMessage(
      "اضغط على الميكروفون للتحدث",
    ),
    "tax": MessageLookupByLibrary.simpleMessage("ضريبة"),
    "terrible": MessageLookupByLibrary.simpleMessage("رهيب"),
    "thailand": MessageLookupByLibrary.simpleMessage("Thai"),
    "theFieldIsRequired": m40,
    "thisAppIsNotActive": MessageLookupByLibrary.simpleMessage(
      "هذا التطبيق غير نشط حاليا",
    ),
    "thisDateIsNotAvailable": MessageLookupByLibrary.simpleMessage(
      "هذا التاريخ غير متوفر",
    ),
    "thisFeatureDoesNotSupportTheCurrentLanguage":
        MessageLookupByLibrary.simpleMessage(
          "هذه الميزة لا تدعم اللغة الحالية",
        ),
    "thisIsCustomerRole": MessageLookupByLibrary.simpleMessage(
      "هذا هو دور العميل",
    ),
    "thisIsVendorRole": MessageLookupByLibrary.simpleMessage(
      "هذا هو دور البائع",
    ),
    "thisPlatformNotSupportWebview": MessageLookupByLibrary.simpleMessage(
      "هذا النظام لا يدعم عرض الويب",
    ),
    "thisProductNotSupport": MessageLookupByLibrary.simpleMessage(
      "هذا المنتج لا يدعم",
    ),
    "thisWebsiteIsNotActive": MessageLookupByLibrary.simpleMessage(
      "هذا الموقع غير نشط حاليا",
    ),
    "thursday": MessageLookupByLibrary.simpleMessage("الخميس"),
    "tickets": MessageLookupByLibrary.simpleMessage("تذاكر"),
    "time": MessageLookupByLibrary.simpleMessage("زمن"),
    "title": MessageLookupByLibrary.simpleMessage("عنوان"),
    "titleAToZ": MessageLookupByLibrary.simpleMessage("أبجدي: تصاعدي"),
    "titleZToA": MessageLookupByLibrary.simpleMessage("أبجدي: تنازلي"),
    "to": MessageLookupByLibrary.simpleMessage("إلى"),
    "tooManyFaildedLogin": MessageLookupByLibrary.simpleMessage(
      "عدد كبير جداً من محاولات تسجيل الدخول الفاشلة. الرجاء معاودة المحاولة في وقت لاحق.",
    ),
    "topUp": MessageLookupByLibrary.simpleMessage("اشحن"),
    "topUpProductNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على منتج التعبئة",
    ),
    "total": MessageLookupByLibrary.simpleMessage("الاجمالي"),
    "totalCartValue": MessageLookupByLibrary.simpleMessage(
      "مجموع الطلبات يجب أن لايقل عن ",
    ),
    "totalPrice": MessageLookupByLibrary.simpleMessage("السعر الكلي"),
    "totalPriceNumber": m41,
    "totalProductItems": m42,
    "totalProducts": m43,
    "totalProductsNumber": m44,
    "totalTax": MessageLookupByLibrary.simpleMessage("مجموع الضريبة"),
    "trackingNumberIs": MessageLookupByLibrary.simpleMessage("رقم التتبع هو"),
    "trackingPage": MessageLookupByLibrary.simpleMessage("صفحة التتبع"),
    "transactionCancelled": MessageLookupByLibrary.simpleMessage(
      "تم الغاء المعاملة",
    ),
    "transactionDetail": MessageLookupByLibrary.simpleMessage("تفاصيل الصفقة"),
    "transactionFailded": MessageLookupByLibrary.simpleMessage("فشل الاجراء"),
    "transactionFee": MessageLookupByLibrary.simpleMessage("رسوم التحويل"),
    "transactionResult": MessageLookupByLibrary.simpleMessage("نتيجة الصفقة"),
    "transfer": MessageLookupByLibrary.simpleMessage("نقل"),
    "transferConfirm": MessageLookupByLibrary.simpleMessage("تأكيد النقل"),
    "transferErrorMessage": MessageLookupByLibrary.simpleMessage(
      "لا يمكنك النقل لهذا المستخدم",
    ),
    "transferFailed": MessageLookupByLibrary.simpleMessage("فشل النقل"),
    "transferSuccess": MessageLookupByLibrary.simpleMessage("نجاح النقل"),
    "tuesday": MessageLookupByLibrary.simpleMessage("الثلاثاء"),
    "turkish": MessageLookupByLibrary.simpleMessage("التركية"),
    "turnOnBle": MessageLookupByLibrary.simpleMessage("قم بتشغيل البلوتوث"),
    "typeAMessage": MessageLookupByLibrary.simpleMessage("اكتب رسالة ..."),
    "typeYourMessage": MessageLookupByLibrary.simpleMessage(
      "اكتب رسالتك هنا...",
    ),
    "typing": MessageLookupByLibrary.simpleMessage("كتابة ..."),
    "ukrainian": MessageLookupByLibrary.simpleMessage("الأوكراني"),
    "unavailable": MessageLookupByLibrary.simpleMessage("غير متوفر"),
    "undo": MessageLookupByLibrary.simpleMessage("تراجع"),
    "unpaid": MessageLookupByLibrary.simpleMessage("غير مدفوع"),
    "update": MessageLookupByLibrary.simpleMessage("تحديث"),
    "updateFailed": MessageLookupByLibrary.simpleMessage("فشل التحديث!"),
    "updateInfo": MessageLookupByLibrary.simpleMessage("تحديث المعلومات"),
    "updatePassword": MessageLookupByLibrary.simpleMessage("تحديث كلمة السر"),
    "updateProfile": MessageLookupByLibrary.simpleMessage("تحديث الملف الشخصي"),
    "updateStatus": MessageLookupByLibrary.simpleMessage("تحديث الحالة"),
    "updateSuccess": MessageLookupByLibrary.simpleMessage("تم التحديث بنجاح!"),
    "updateUserInfor": MessageLookupByLibrary.simpleMessage(
      "تحديث معلومات المستخدم",
    ),
    "uploadFile": MessageLookupByLibrary.simpleMessage("رفع ملف"),
    "uploadImage": MessageLookupByLibrary.simpleMessage("تحميل الصور"),
    "uploadProduct": MessageLookupByLibrary.simpleMessage("تحميل المنتج"),
    "uploading": MessageLookupByLibrary.simpleMessage("تحميل"),
    "url": MessageLookupByLibrary.simpleMessage("الرابط"),
    "useMaximumPointDiscount": m45,
    "useNow": MessageLookupByLibrary.simpleMessage("استخدم الان"),
    "useThisImage": MessageLookupByLibrary.simpleMessage("استخدم هذه الصورة"),
    "userExists": MessageLookupByLibrary.simpleMessage(
      "اسم المستخدم / البريد الإكتروني هذا غير متوفر.",
    ),
    "userNameInCorrect": MessageLookupByLibrary.simpleMessage(
      "خطأ في اسم المستخدم أو كلمة السر.",
    ),
    "username": MessageLookupByLibrary.simpleMessage("اسم المستخدم"),
    "usernameAndPasswordRequired": MessageLookupByLibrary.simpleMessage(
      "مطلوب اسم المستخدم وكلمة المرور",
    ),
    "vacationMessage": MessageLookupByLibrary.simpleMessage("رسالة الإجازة"),
    "vacationType": MessageLookupByLibrary.simpleMessage("نوع الاجازة"),
    "validUntilDate": m46,
    "variable": MessageLookupByLibrary.simpleMessage("متعدد"),
    "variation": MessageLookupByLibrary.simpleMessage("التخصيص"),
    "vendor": MessageLookupByLibrary.simpleMessage("بائع"),
    "vendorAdmin": MessageLookupByLibrary.simpleMessage("خاص بالبائع"),
    "vendorInfo": MessageLookupByLibrary.simpleMessage("معلومات البائع"),
    "verificationCode": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق (6 أرقام)",
    ),
    "verifySMSCode": MessageLookupByLibrary.simpleMessage("تحقق من الرمز"),
    "viaWallet": MessageLookupByLibrary.simpleMessage("عبر المحفظة"),
    "video": MessageLookupByLibrary.simpleMessage("فيديو"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("الفيتنامية"),
    "view": MessageLookupByLibrary.simpleMessage("رأي"),
    "viewCart": MessageLookupByLibrary.simpleMessage("عرض السلة"),
    "viewDetail": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
    "viewMore": MessageLookupByLibrary.simpleMessage("عرض المزيد"),
    "viewOnGoogleMaps": MessageLookupByLibrary.simpleMessage(
      "عرض على خرائط جوجل",
    ),
    "viewOrder": MessageLookupByLibrary.simpleMessage("مشاهدة الطلب"),
    "viewRecentTransactions": MessageLookupByLibrary.simpleMessage(
      "عرض المعاملات الأخيرة",
    ),
    "visible": MessageLookupByLibrary.simpleMessage("مرئي"),
    "visitStore": MessageLookupByLibrary.simpleMessage("زيارة المتجر"),
    "waitForLoad": MessageLookupByLibrary.simpleMessage(
      "في انتظار تحميل الصورة",
    ),
    "waitForPost": MessageLookupByLibrary.simpleMessage("في انتظار المنتج"),
    "waitingForConfirmation": MessageLookupByLibrary.simpleMessage(
      "في انتظار التأكيد",
    ),
    "walletBalance": MessageLookupByLibrary.simpleMessage("رصيد المحفظة"),
    "walletName": MessageLookupByLibrary.simpleMessage("اسم المحفظة"),
    "warning": m47,
    "warningCurrencyMessageForWallet": m48,
    "weFoundBlogs": MessageLookupByLibrary.simpleMessage("وجدنا بلوق (ق)"),
    "weFoundProducts": m49,
    "weNeedCameraAccessTo": MessageLookupByLibrary.simpleMessage(
      "نحتاج إلى الوصول إلى الكاميرا للبحث عن رمز الاستجابة السريعة أو الرمز الشريطي.",
    ),
    "weSentAnOTPTo": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز المصادقة إلى",
    ),
    "weWillSendYouNotification": MessageLookupByLibrary.simpleMessage(
      "سنرسل إليك إشعارات عند توفر منتجات جديدة أو عند توفر عروض. يمكنك دائمًا تغيير هذا الضبط في الإعدادات.",
    ),
    "webView": MessageLookupByLibrary.simpleMessage("عرض الويب"),
    "wednesday": MessageLookupByLibrary.simpleMessage("الأربعاء"),
    "week": m50,
    "welcome": MessageLookupByLibrary.simpleMessage("أهلاً بك"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("مرحبا بعودتك"),
    "welcomeRegister": MessageLookupByLibrary.simpleMessage(
      "ابدأ رحلة التسوق معنا الآن",
    ),
    "welcomeUser": m51,
    "whichLanguageDoYouPrefer": MessageLookupByLibrary.simpleMessage(
      "اي لغة تفضل؟",
    ),
    "wholesaleRegisterMsg": MessageLookupByLibrary.simpleMessage(
      "يرجى التواصل مع المسؤول للموافقة على تسجيلك.",
    ),
    "womanCollections": MessageLookupByLibrary.simpleMessage("مجموعات المرأة"),
    "writeComment": MessageLookupByLibrary.simpleMessage("اكتب تعليقك"),
    "writeYourNote": MessageLookupByLibrary.simpleMessage("اكتب ملاحظاتك"),
    "yearsAgo": m52,
    "yes": MessageLookupByLibrary.simpleMessage("نعم"),
    "youCanOnlyOrderSingleStore": MessageLookupByLibrary.simpleMessage(
      "يمكنك الشراء فقط من متجر واحد خلال نفس العملية.",
    ),
    "youCanOnlyPurchase": MessageLookupByLibrary.simpleMessage(
      "يمكنك شراء فقط ",
    ),
    "youHaveAssignedToOrder": m53,
    "youHaveBeenSaveAddressYourLocal": MessageLookupByLibrary.simpleMessage(
      "لقد تم حفظ العنوان في منطقتك",
    ),
    "youHaveNewOrder": MessageLookupByLibrary.simpleMessage("لديك طلب جديد"),
    "youHavePoints": MessageLookupByLibrary.simpleMessage("لديك \$point نقطة"),
    "youMightAlsoLike": MessageLookupByLibrary.simpleMessage("قد يعجبك أيضاً"),
    "youNeedToLoginCheckout": MessageLookupByLibrary.simpleMessage(
      "تحتاج إلى تسجيل الدخول للخروج",
    ),
    "youNotBeAsked": MessageLookupByLibrary.simpleMessage(
      "لن يتم سؤالك في المرة القادمة بعد الانتهاء",
    ),
    "yourAccountIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "حسابك قيد المراجعة. يرجى الاتصال بالمسؤول إذا كنت بحاجة إلى أي مساعدة.",
    ),
    "yourAddressExistYourLocal": MessageLookupByLibrary.simpleMessage(
      "عنوانك موجود في منطقتك",
    ),
    "yourAddressHasBeenSaved": MessageLookupByLibrary.simpleMessage(
      "تم حفظ العنوان في التخزين المحلي الخاص بك",
    ),
    "yourApplicationIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "طلبك قيد المراجعة.",
    ),
    "yourBagIsEmpty": MessageLookupByLibrary.simpleMessage("السلة فارغة"),
    "yourBookingDetail": MessageLookupByLibrary.simpleMessage("تفاصيل حجزك"),
    "yourEarningsThisMonth": MessageLookupByLibrary.simpleMessage(
      "أرباحك هذا الشهر",
    ),
    "yourNote": MessageLookupByLibrary.simpleMessage("ملاحظاتك"),
    "yourOrderHasBeenAdded": MessageLookupByLibrary.simpleMessage(
      "تمت إضافة طلبك",
    ),
    "yourOrderIsConfirmed": MessageLookupByLibrary.simpleMessage(
      "تم تأكيد طلبك!",
    ),
    "yourOrderIsEmpty": MessageLookupByLibrary.simpleMessage("طلباتك فارغة"),
    "yourOrderIsEmptyMsg": MessageLookupByLibrary.simpleMessage(
      "يبدو أنك لم تقم بإضافة أي عناصر.\nابدأ بالتسوق لملئه.",
    ),
    "yourOrderNumber": m54,
    "yourOrders": MessageLookupByLibrary.simpleMessage("طلباتك"),
    "yourProductIsUnderReview": MessageLookupByLibrary.simpleMessage(
      "منتجك قيد المراجعة",
    ),
    "yourUsernameEmail": MessageLookupByLibrary.simpleMessage(
      "اسم المستخدم أو البريد الإكتروني الخاص بك",
    ),
    "zipCode": MessageLookupByLibrary.simpleMessage("الدور"),
    "zipCodeIsRequired": MessageLookupByLibrary.simpleMessage(
      "خانة الرمز البريدي مطلوبة",
    ),
  };
}
