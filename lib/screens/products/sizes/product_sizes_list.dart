import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../../common/tools/tools.dart';
import '../../../models/extra_setting_model.dart';
import '../../checkout/widgets/convet_city_lang.dart';
import 'choose_size_card.dart';

class ProductSizesList extends StatelessWidget {
  final List<ExtraSettingsModel> sizes;
  final ValueNotifier<String>? selectedSize;
  final Function setState;

  const ProductSizesList(
      {super.key,
      required this.selectedSize,
      required this.sizes,
      required this.setState});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: sizes.indexed.map((indexedSizes) {
        final size = indexedSizes.$2;

        if (selectedSize?.value.isEmpty ?? true) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              var firstAvailableSize =
                  sizes.firstWhereOrNull((size) => size.stock != 0
                      // !size.isOutOfStock,
                      );

              if (firstAvailableSize != null) {
                selectedSize?.value = firstAvailableSize.name;
              }
            });
          });

          // WidgetsBinding.instance.addPostFrameCallback((_) {
          //   setState(() {
          //     selectedSize?.value = sizes.first.name;
          //   });
          // });
        }

        final isSizeSelected = selectedSize?.value == size.name;

        void onSelectSize() {
          if (size.isOutOfStock) {
            Tools.showSnackBar(
                ScaffoldMessenger.of(context),
                isEnglish(context)
                    ? 'This size is out of stock'
                    : 'هذا الحجم غير متوفر');
            return;
          }

          setState(() {
            selectedSize?.value = size.name;
          });
        }

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: Wrap(
            children: [
              GestureDetector(
                onTap: onSelectSize,
                child: ChooseSizeCard(
                  size: size.name,
                  isSelected: isSizeSelected,
                  isOutOffStock: size.isOutOfStock,
                ),
              )
            ],
          ),
        );
      }).toList(),
    );
  }
}
