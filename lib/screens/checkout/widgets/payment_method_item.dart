import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../common/config.dart';
import '../../../common/constants.dart';
import '../../../generated/l10n.dart';
import '../../../models/index.dart' show PaymentMethod;
import '../../../services/index.dart';
import '../../../widgets/common/flux_image.dart';
import '../../../widgets/html/index.dart';

bool isCODOrOnlinePayment(String? payment) {
  return isCOD(payment) || isOnlinePayment(payment) || isPaypal(payment);
}

bool isOnlinePayment(String? paymentMethod) {
  return paymentMethod?.toLowerCase() == 'online payment';
}

bool isPaypal(String? paymentMethod) {
  return paymentMethod?.toLowerCase() == 'paypal';
}

bool isCOD(String? paymentMethod) {
  return paymentMethod?.toLowerCase() == 'cod';
}

class PaymentMethodItem extends StatelessWidget {
  const PaymentMethodItem({
    super.key,
    required this.paymentMethod,
    this.onSelected,
    this.selectedId,
    this.descWidget,
    this.useDesktopStyle = false,
  });

  final PaymentMethod paymentMethod;
  final Function(String?)? onSelected;
  final String? selectedId;
  final Widget? descWidget;
  final bool useDesktopStyle;

  @override
  Widget build(BuildContext context) {
    final isSelected = paymentMethod.id == selectedId;

    final isCOD = isCODOrOnlinePayment(paymentMethod.title);
    final isInstapayLink = paymentMethod.description != null &&
        (paymentMethod.description?.contains('http') == true ||
            paymentMethod.description?.contains('https') == true);

    return Column(
      children: <Widget>[
        InkWell(
          onTap: () {
            if (onSelected != null) onSelected!(paymentMethod.id);
          },
          child: Container(
            decoration: BoxDecoration(
              color: paymentMethod.id == selectedId
                  ? Theme.of(context).primaryColorLight
                  : Colors.transparent,
              borderRadius: useDesktopStyle ? BorderRadius.circular(10) : null,
              border: useDesktopStyle
                  ? Border.all(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Theme.of(context)
                              .colorScheme
                              .secondary
                              .withOpacity(0.2),
                      width: 2,
                    )
                  : null,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
              child: Column(
                children: [
                  Row(
                    children: <Widget>[
                      Radio<String?>(
                        value: paymentMethod.id,
                        groupValue: selectedId,
                        onChanged: onSelected,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Builder(builder: (context) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Row(
                                children: [
                                  if (paymentMethod.title != null) ...[
                                    Flexible(
                                      fit: useDesktopStyle
                                          ? FlexFit.tight
                                          : FlexFit.loose,
                                      child: Services()
                                          .widget
                                          .renderShippingPaymentTitle(
                                              context, paymentMethod.title!),
                                    ),
                                    const SizedBox(width: 15),
                                  ],
                                  if (kPayments[paymentMethod.id] != null)
                                    FluxImage(
                                      imageUrl: kPayments[paymentMethod.id],
                                      height: 30,
                                    ),
                                ],
                              ),
                              if (paymentMethod.description != null)
                                if (paymentMethod.id == selectedId &&
                                    !isInstapayLink) ...[
                                  const SizedBox(height: 5),
                                  Row(
                                    children: [
                                      HtmlWidget(paymentMethod.description!),
                                      const SizedBox(width: 10),
                                      if (!isCOD)
                                        CircleAvatar(
                                          backgroundColor:
                                              kGrey400.withOpacity(0.1),
                                          child: IconButton(
                                            icon: const Icon(Icons.copy),
                                            onPressed: () {
                                              Clipboard.setData(ClipboardData(
                                                  text: paymentMethod
                                                      .description!));

                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    S
                                                        .of(context)
                                                        .copiedToClipboard,
                                                  ),
                                                  duration: const Duration(
                                                      milliseconds: 500),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                            ],
                          );
                        }),
                      ),
                      if (descWidget != null &&
                          paymentMethod.id == selectedId &&
                          !useDesktopStyle)
                        descWidget!
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (useDesktopStyle)
          const SizedBox(height: 15)
        else
          const Divider(height: 1)
      ],
    );
  }
}
