import 'package:flutter/material.dart';
import 'package:inspireui/inspireui.dart';

class TransactionItemSkeleton extends StatelessWidget {
  const TransactionItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0),
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        horizontalTitleGap: 20,
        minVerticalPadding: 4,
        minLeadingWidth: 24,
        leading: Skeleton(
          height: 30,
          width: 30,
        ),
        title: Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              <PERSON>keleton(
                height: 16,
                width: 180,
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 4),
              <PERSON>keleton(
                height: 14,
                width: 100,
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 12),
              <PERSON>keleton(
                height: 10,
                width: 100,
              ),
            ],
          ),
        ),
        trailing: <PERSON>keleton(
          height: 22,
          width: 100,
        ),
      ),
    );
  }
}
