import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:fstore/modules/dynamic_layout/category/category_view_widgets/clothes_category.dart';
import 'package:fstore/modules/dynamic_layout/category/category_view_widgets/default_category.dart';
import 'package:fstore/modules/dynamic_layout/category/web_new_dynamic_category.dart';
import 'package:intrinsic_grid_view/intrinsic_grid_view.dart';
import 'package:provider/provider.dart';

import '../../../common/config.dart';
import '../../../common/constants.dart';
import '../../../context_extensions.dart';
import '../../../frameworks/strapi/services/strapi_service.dart';
import '../../../models/category/category_model.dart';
import '../../../models/entities/category.dart';
import '../../../widgets/common/background_color_widget.dart';
import '../config/category_config.dart';
import '../helper/helper.dart';
import 'category_image_item.dart';
import 'category_view_widgets/accessories_category.dart';
import 'category_view_widgets/main_category_widget.dart';
import 'category_view_widgets/market_category.dart';
import 'category_view_widgets/restaurant_category.dart'
    show RestaurantCategoryImageItem;

/// List of Category Items
class CategoryImages extends StatefulWidget {
  final CategoryConfig config;

  const CategoryImages({required this.config, super.key});

  @override
  State<CategoryImages> createState() => _CategoryImagesState();
}

class _CategoryImagesState extends State<CategoryImages> {
  List<Widget>? _cachedItems;

  @override
  void initState() {
    super.initState();
    final categoryModel = Provider.of<CategoryModel>(context, listen: false);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // if (kIsWeb) {

      if (mainCategories.isEmpty) {
        categoryModel.getMainCategories();
      }

      if (categoryModel.categories == null ||
          categoryModel.categories!.isEmpty) {
        categoryModel.getCategories();
      }

      // } else {
      //   categoryModel.getCategories();
      // }
    });
  }

  List<Widget> listItem({required double maxWidth}) {
    if (_cachedItems != null) {
      return _cachedItems!;
    }

    var items = <Widget>[];
    var sizeWidth;
    var sizeHeight;
    var itemSize = widget.config.commonItemConfig.itemSize;

    if (itemSize != null) {
      sizeWidth = itemSize.width;
      sizeHeight = itemSize.height;
    } else {
      sizeWidth = maxWidth / 3;
    }
    for (var item in widget.config.items) {
      items.add(CategoryImageItem(
          config: item,
          width: sizeWidth,
          height: sizeHeight,
          commonConfig: widget.config.commonItemConfig));
    }

    _cachedItems = items;
    return items;
  }

  Widget renderCategories({double? maxItemWidth}) {
    return Consumer<CategoryModel>(
      builder: (context, categoryModel, child) {
        final categories = categoryModel.categories ?? <Category>[];

        final isDesktop = MediaQuery.sizeOf(context).width > 800;

        printLog('asfssafas ${mainCategories}');

        //TODO-MainCategory
        if (mainCategories.isNotEmpty) {
          return StaggeredGrid.count(
              crossAxisCount: context.screenWidth > 600 ? 3 : 2,
              mainAxisSpacing: 4.0,
              children: mainCategories.map((category) {
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: MainCategoryImageItem(
                    cat: category,
                    width: MediaQuery.sizeOf(context).width / 2.3,
                    height: Layout.isDisplayDesktop(context)
                        ? context.screenHeight / 3
                        : context.screenHeight / 4,
                    commonConfig: widget.config.commonItemConfig,
                  ),
                );
              }).toList());
        }

        if (isDesktop && currentVendor?.isRestaurant == false) {
          return Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Wrap(
              alignment: WrapAlignment.center,
              children: categories.map((category) {
                return WebNewDynamicCategoryImageItem(
                  cat: category,
                  height: 250,
                  width: 250,
                  commonConfig: widget.config.commonItemConfig,
                );
              }).toList(),
            ),
          );
        }

        if (currentVendor?.isRestaurant == true) {
          final restaurantCategoriesList = ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              return RestaurantCategoryImageItem(
                cat: categories[index],
                commonConfig: widget.config.commonItemConfig,
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                  width: Layout.isDisplayDesktop(context) ? 20 : 10);
            },
          );

          if (Layout.isDisplayDesktop(context)) {
            return SizedBox(
              height: 200,
              child: Center(
                child: restaurantCategoriesList,
              ),
            );
          }

          return SizedBox(
            height: context.isTablet ? 170.0 : 150.0,
            child: restaurantCategoriesList,
          );
        }

        if (currentVendor?.isClothes == true) {
          return StaggeredGrid.count(
              crossAxisCount: context.screenWidth > 600 ? 3 : 2,
              mainAxisSpacing: 4.0,
              children: categories.map((category) {
                return ClothesCategoryImageItem(
                  cat: category,
                  width: MediaQuery.sizeOf(context).width / 2.3,
                  height: context.screenHeight / 2.8,
                  // itemSize?.height,
                  commonConfig: widget.config.commonItemConfig,
                );
              }).toList());
        }

        if (currentVendor?.isAccessories == true) {
          return Wrap(
            alignment: WrapAlignment.start,
            children: categories.map((category) {
              return AccessoriesCategoryImageItem(
                cat: category,
                width: MediaQuery.sizeOf(context).width / 2.3,
                height: 320.0,
                // itemSize?.height,
                commonConfig: widget.config.commonItemConfig,
              );
            }).toList(),
          );
        }

        if (currentVendor?.isMarket == true) {
          return Padding(
            padding: const EdgeInsets.only(top: 12),
            child: SizedBox(
                height: categories.length < 12
                    ? context.screenHeight / 5.3
                    : categories.length < 18
                        ? context.screenHeight / 2.6
                        : context.screenHeight / 1.6,
                child: IntrinsicGridView.horizontal(
                    padding: const EdgeInsets.only(top: 12),
                    rowCount: categories.length < 12
                        ? 1
                        : categories.length < 18
                            ? 2
                            : 3,
                    verticalSpace: 10,
                    horizontalSpace: 20,
                    children: categories.map((category) {
                      return MarketCategoryImageItem(
                        cat: category,
                        commonConfig: widget.config.commonItemConfig,
                      );

                      // return BaseCategoryWidget(
                      //   cat: category,
                      //   commonConfig: widget.config.commonItemConfig,
                      // );
                    }).toList())),
          );
        }

        return SizedBox(
          height: 240.0,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: categories.map((category) {
                return DefaultCategoryImageItem(
                  cat: category,
                  width: MediaQuery.sizeOf(context).width / 2.3,
                  commonConfig: widget.config.commonItemConfig,
                );
              }).toList(),
            ),
          ),
        );

        return SizedBox(
          height: 240.0,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: categories.map((category) {
              return DefaultCategoryImageItem(
                cat: category,
                width: MediaQuery.sizeOf(context).width / 2.3,
                commonConfig: widget.config.commonItemConfig,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var itemSize = widget.config.commonItemConfig.itemSize;
    var sizeHeight = itemSize?.height;

    return BackgroundColorWidget(
      enable: widget.config.enableBackground,
      margin: EdgeInsets.only(
        left: widget.config.marginLeft,
        right: widget.config.marginRight,
        top: widget.config.marginTop,
        bottom: widget.config.marginBottom,
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          var maxWidth = constraints.maxWidth -
              widget.config.marginLeft -
              widget.config.marginRight;
          final width = maxWidth / 10;
          final heightList = width + 22;

          return renderCategories();
        },
      ),
    );
  }
}
