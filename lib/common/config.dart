import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:fstore/models/entities/seo/seo_meta_twitter.dart';
import 'package:fstore/models/vendor/vendor_model.dart';
import 'package:fstore/services/get_storage_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../common/tools.dart';
import '../models/entities/index.dart';
import '../models/entities/seo/meta_seo_model.dart';
import '../models/entities/seo/seo_meta_keyvalue.dart';
import '../models/entities/seo/seo_meta_name_content.dart';
import '../models/entities/seo/seo_meta_property_content.dart';
import '../models/entities/seo/seo_twitter_card.dart';
import '../modules/dynamic_layout/index.dart';
import '../services/service_config.dart';
import '../services/services.dart';
import 'config/configuration_utils.dart';
import 'config/loading/index.dart';
import 'config/models/address_field_config.dart';
import 'config/models/app_rating_config.dart';
import 'config/models/branch_config.dart';
import 'config/models/index.dart';
import 'config/models/onboarding_config.dart';
import 'config/models/vendor_config.dart';
import 'config/multi_site.dart';
import 'constants.dart';
import 'constants/local_keys.dart';
import 'logger.dart';

part 'config/advertise.dart';
part 'config/blogs.dart';
part 'config/configurations.dart';
part 'config/data_mapping.dart';
part 'config/default_env.dart';
part 'config/dynamic_link.dart';
part 'config/general.dart';
part 'config/languages.dart';
part 'config/loading.dart';
part 'config/onboarding.dart';
part 'config/payments.dart';
part 'config/products.dart';
part 'config/smartchat.dart';
part 'config/vendor.dart';

Map get serverConfig => Configurations.serverConfig;

List<PromoCodeModel> promoCodesList = [];

PromoCodeModel? selectedPromoCode;

String _businessName = 'lelt-elkadr';
// String _businessName = 'default';
// String _businessName = 'clothes';
// String _businessName = 'restaurant';

String get savedTemplate =>
    GetStorageService.getData(key: LocalKeys.template) ?? '';

String vendorBusinessName = _businessName != 'default'
    ? _businessName
    : savedTemplate.isNotEmpty && !kIsWeb
        ? savedTemplate
        : _businessName;

VendorModel? currentVendor;

bool get isIdea2App {
  final businessName = currentVendor?.businessName?.toLowerCase() ?? '';

  Log.w('Current_Vendor $businessName');

  return (businessName == 'clothes' || vendorBusinessName == 'clothes') ||
      (businessName == 'market' || vendorBusinessName == 'market') ||
      (businessName == 'gifts' || vendorBusinessName == 'gifts') ||
      (businessName == 'restaurant' || vendorBusinessName == 'restaurant') ||
      (businessName == 'accessories' || vendorBusinessName == 'accessories') ||
      (businessName == 'restaurants' || vendorBusinessName == 'restaurants') ||
      (businessName == 'shoes' || vendorBusinessName == 'shoes') ||
      (businessName == 'medical' || vendorBusinessName == 'medical') ||
      (businessName == 'general' || vendorBusinessName == 'general') ||
      (businessName == 'default' || vendorBusinessName == 'default') ||
      (businessName == 'electronics' || vendorBusinessName == 'electronics') ||
      (businessName == 'others' || vendorBusinessName == 'others');
}

bool get isAccessories {
  return currentVendor?.businessType == 'accessories';
}

bool get isDefault {
  return currentVendor?.businessType == 'default';
}

bool get isMarket {
  return currentVendor?.businessType == 'market';
}

bool get isRestaurant {
  return currentVendor?.businessType == 'restaurant';
}

bool isFeaturedLayout(String? name) {
  final isFeaturedSection =
      name?.toLowerCase() == Configurations.featuredEnName ||
          name?.toLowerCase() == Configurations.featuredEnName2 ||
          name?.toLowerCase() == Configurations.featuredArName ||
          name?.toLowerCase() == Configurations.featuredArName2;

  return isFeaturedSection;
}

String get getMainColorByCurrentVendor {
  final color = isAccessories
      ? 'ffac9582'
      : isRestaurant
          ? 'ffff5f1c'
          : isMarket
              ? 'ff1aa137'
              : isDefault
                  ? 'ff6575ff'
                  : 'ff67b0ff';

  return color;
}

bool canShowVendorLogo() {
  return (currentVendor == null || isIdea2App) ||
      (currentVendor != null &&
          currentVendor?.logoUrl != null &&
          currentVendor?.logoUrl != '');
}

final metaSeoModel = MetaSeoModel(
  author: 'Idea2App Customer',
  description:
      'Explore a wide range of content and enjoy a seamless browsing experience on our platform. Discover now!',
  keywords: ['Idea2App Customer'],
  viewport:
      'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
  httpEquiv: 'Content-Type',
  ogTitle: 'Idea2App Customer',
  ogDescription: 'Idea2App Customer - Explore Now!',
  ogImage: 'https://idea2app.com/wp-content/uploads/2021/06/idea2app-logo.png',
  metaTwitter: const SeoMetaTwitter(
    title: 'Idea2App Customer - Explore Now!',
    description: 'Idea2App Customer - Explore Now!',
    image: 'https://idea2app.com/wp-content/uploads/2021/06/idea2app-logo.png',
    card: SeoTwitterCard.summaryLargeImage,
  ),
  metaKeyValue: const SeoMetaKeyValue(
    key: 'Idea2App_Customer',
    value: 'Idea2App_Customer',
  ),
  metaPropertyContent: const SeoMetaPropertyContent(
    property: 'og:site_name',
    content: 'Idea2App Customer',
  ),
  metaNameContent: const SeoMetaNameContent(
    name: 'application-name',
    content: 'Idea2App Customer',
  ),
);

String get androidDownloadLink => Configurations.androidDownloadLink;

String get iosDownloadLink => Configurations.iosDownloadLink;

String get aboutUsLink => Configurations.aboutUsLink;

String get privacyLink => Configurations.privacyLink;

String get supportLink => Configurations.supportLink;

String get idea2AppCopyRightUrl => Configurations.idea2AppCopyRightUrl;

String get loginPlaceHolderImage => Configurations.loginPlaceHolderImage;
