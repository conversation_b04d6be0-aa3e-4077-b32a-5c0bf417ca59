import 'package:universal_html/html.dart' as html;

void setWebFavIcon(String? link) {
  if (link == null) return;
  html.LinkElement? favicon =
      html.document.querySelector('link[rel="icon"]') as html.LinkElement?;

  if (favicon != null) {
    favicon.href = link;
  } else {
    favicon = html.LinkElement()
      ..rel = 'icon'
      ..href = link;
    html.document.head!.append(favicon);
  }
}
