import 'package:collection/collection.dart';

import '../../../common/constants.dart';
import '../../../data/boxes.dart';
import '../../../services/index.dart';
import '../../extra_setting_model.dart';
import '../../index.dart';
import '../cart_item_meta_data.dart';
import 'cart_mixin.dart';

/// Everything relate to Local storage
mixin LocalMixin on CartMixin {
  void saveCartToLocal(String key,
      {Product? product,
      int? quantity = 1,
      String? size,
      String? color,
      CartItemMetaData? cartItemMetaData}) {
    try {
      // var oldQuantity = 0;

      // if (items != null && items.isNotEmpty) {
      //   for (var item in items) {
      //     final productItem = Product.fromLocalJson(item['product']);
      //     final ids = productItem.id.toString();
      //     final colorItem = item['color'];
      //     final sizeItem = item['size'];
      //
      //     if (product?.id == ids && color == colorItem && size == sizeItem) {
      //       oldQuantity = item['quantity'];
      //       break;
      //     }
      //   }
      // }

      var items = UserBox().productsInCart;

      // var newQuantity = quantity! + oldQuantity;

      // printLog('Old_Quantity $oldQuantity - New_Quantity $newQuantity');

      product?.selectedColor = color;
      product?.selectedSize = size;

      // final selectedProductSize = selectedSize[product.id]?.value;

      final sizePrice = product?.sizes
          .firstWhereOrNull((element) => element.name == product.selectedSize)
          ?.price;
      printLog('safasfsafs222323f ${sizePrice}');

      // final copiedProduct = product?.copyWith(
      //   price: sizePrice ?? product.price,
      //   regularPrice: sizePrice ?? product.regularPrice,
      //   salePrice: sizePrice ?? product.salePrice,
      // );

      product?.price = sizePrice?.toString() ?? product.price;
      product?.regularPrice = sizePrice?.toString() ?? product.regularPrice;
      product?.salePrice = sizePrice?.toString() ?? product.salePrice;

      printLog('ffffggdgdgdgdgdfsfsf ${product?.toJson()}');

      final newItem = {
        'product': product?.toJson(),
        'quantity': quantity,
        'size': size,
        'color': color,
        'price': sizePrice ?? product?.price,
        'key': key,
        'cartItemMetaData': cartItemMetaData?.toJson(),
      };

      printLog('QQQUANTITY: ${items?.length}');

      if (items != null && items.isNotEmpty) {
        final existed =
            items.firstWhereOrNull((item) => item['key'] == key) != null;
        if (existed) {
          items =
              items.map((item) => item['key'] == key ? newItem : item).toList();
        } else {
          items.add(newItem);
        }
      } else {
        items = [newItem];
      }

      var selectedSizeAndColorList =
          UserBox().selectedSizeAndColor[product!.id.toString()] ?? [];

      var indexToUpdate = selectedSizeAndColorList.indexWhere(
          (element) => element.size == size && element.color == color);

      if (indexToUpdate != -1) {
        printLog(
            'selectedSizeAndColorList[indexToUpdate]Before: ${selectedSizeAndColorList[indexToUpdate].quantity}');

        selectedSizeAndColorList[indexToUpdate].quantity = quantity;

        printLog(
            'selectedSizeAndColorList[indexToUpdate].After: ${selectedSizeAndColorList[indexToUpdate].quantity}\nUpdated quantity: $quantity');
      } else {
        selectedSizeAndColorList.add(SelectedSizeAndColor(
          size: size,
          color: color,
          quantity: quantity,
          price: sizePrice,
        ));

        printLog('selectedSizeAndColorListAdd: $quantity');
      }

      UserBox().selectedSizeAndColor = {
        ...UserBox().selectedSizeAndColor,
        product.id.toString(): selectedSizeAndColorList,
      };

      UserBox().productsInCart = items;

      printLog('QQQUANTITY2222: ${items.length}');
    } catch (err) {
      printLog('[saveCartToLocal] failed: $err');
    }
  }

  // void saveCartToLocal(String key,
  //     {Product? product,
  //     int? quantity = 1,
  //     String? size,
  //     String? color,
  //     CartItemMetaData? cartItemMetaData}) {
  //   try {
  //     var oldQuantity = 0;
  //
  //     var items = UserBox().productsInCart;
  //
  //     if (items != null && items.isNotEmpty) {
  //       for (var item in items) {
  //         final productItem = Product.fromLocalJson(item['product']);
  //         final ids = productItem.id.toString();
  //         final colorItem = item['color'];
  //         final sizeItem = item['size'];
  //
  //         if (product?.id == ids && color == colorItem && size == sizeItem) {
  //           oldQuantity = item['quantity'];
  //           break;
  //         }
  //       }
  //     }
  //
  //     quantity = quantity! + oldQuantity;
  //
  //     product?.selectedColor = color;
  //     product?.selectedSize = size;
  //
  //     final newItem = {
  //       'product': product?.toJson(),
  //       'quantity': quantity,
  //       'size': size,
  //       'color': color,
  //       'key': key,
  //       'cartItemMetaData': cartItemMetaData?.toJson(),
  //     };
  //
  //     if (items != null && items.isNotEmpty) {
  //       final existed =
  //           items.firstWhereOrNull((item) => item['key'] == key) != null;
  //       if (existed) {
  //         items =
  //             items.map((item) => item['key'] == key ? newItem : item).toList();
  //       } else {
  //         items.add(newItem);
  //       }
  //     } else {
  //       items = [newItem];
  //     }
  //
  //     var selectedSizeAndColorList =
  //         UserBox().selectedSizeAndColor[product!.id.toString()] ?? [];
  //     var indexToUpdate = selectedSizeAndColorList.indexWhere(
  //         (element) => element.size == size && element.color == color);
  //
  //     if (indexToUpdate != -1) {
  //       selectedSizeAndColorList[indexToUpdate].quantity = quantity;
  //     } else {
  //       selectedSizeAndColorList.add(SelectedSizeAndColor(
  //         size: size,
  //         color: color,
  //         quantity: quantity,
  //       ));
  //     }
  //
  //     UserBox().selectedSizeAndColor = {
  //       ...UserBox().selectedSizeAndColor,
  //       product.id.toString(): selectedSizeAndColorList,
  //     };
  //
  //     UserBox().productsInCart = items;
  //   } catch (err) {
  //     printLog('[saveCartToLocal] failed: $err');
  //   }
  // }

  // void saveCartToLocal(String key,
  //     {Product? product,
  //     int? quantity = 1,
  //     String? size,
  //     String? color,
  //     CartItemMetaData? cartItemMetaData}) {
  //   try {
  //     log('afafasfsaf $quantity');
  //
  //     //? get old quantity
  //     var oldQuantity = 0;
  //     var items = UserBox().productsInCart;
  //
  //     if (items != null && items.isNotEmpty) {
  //       for (var item in items) {
  //         final productItem = Product.fromLocalJson(item['product']);
  //         final ids = productItem.id.toString();
  //         final colorItem = item['color'];
  //         final sizeItem = item['size'];
  //
  //         if (product?.id == ids && color == colorItem && size == sizeItem) {
  //           oldQuantity = item['quantity'];
  //           break;
  //         }
  //       }
  //     }
  //
  //     //? update quantity
  //     quantity = quantity! + oldQuantity;
  //
  //     log('Old_Quantity $oldQuantity - New_Quantity $quantity');
  //
  //     product?.selectedColor = color;
  //     product?.selectedSize = size;
  //
  //     final newItem = {
  //       'product': product?.toJson(),
  //       'quantity': quantity,
  //       // 'variation': variation != null ? variation.toJson() : 'null',
  //       // 'options': options,
  //       'size': size,
  //       'color': color,
  //       'key': key,
  //       'cartItemMetaData': cartItemMetaData?.toJson(),
  //     };
  //
  //     // var items = UserBox().productsInCart;
  //
  //     if (items != null && items.isNotEmpty) {
  //       var isExist = false;
  //       for (var item in items) {
  //         final productItem = Product.fromLocalJson(item['product']);
  //         final ids = productItem.id.toString();
  //         final colorItem = item['color'];
  //         final sizeItem = item['size'];
  //
  //         if (product?.id == ids && color == colorItem && size == sizeItem) {
  //           isExist = true;
  //           break;
  //         }
  //       }
  //       if (!isExist) {
  //         items.add(newItem);
  //       } else {
  //         items = items.map((item) {
  //           final productItem = Product.fromLocalJson(item['product']);
  //           final ids = productItem.id.toString();
  //           final colorItem = item['color'];
  //           final sizeItem = item['size'];
  //           final quantity = item['quantity'];
  //
  //           if (product?.id == ids && color == colorItem && size == sizeItem) {
  //             item['quantity'] = item['quantity'] + quantity!;
  //           }
  //
  //           return item;
  //         }).toList();
  //       }
  //     } else {
  //       items = [newItem];
  //     }
  //
  //     var selectedSizeAndColorList =
  //         UserBox().selectedSizeAndColor[product!.id.toString()] ?? [];
  //     var indexToUpdate = selectedSizeAndColorList.indexWhere(
  //         (element) => element.size == size && element.color == color);
  //
  //     if (indexToUpdate != -1) {
  //       // Update the quantity of the existing SelectedSizeAndColor object
  //       selectedSizeAndColorList[indexToUpdate].quantity = quantity;
  //     } else {
  //       // Add a new SelectedSizeAndColor object
  //       selectedSizeAndColorList.add(SelectedSizeAndColor(
  //         size: size,
  //         color: color,
  //         quantity: quantity,
  //       ));
  //     }
  //
  //     UserBox().selectedSizeAndColor = {
  //       ...UserBox().selectedSizeAndColor,
  //       product.id.toString(): selectedSizeAndColorList,
  //     };
  //
  //     UserBox().productsInCart = items;
  //   } catch (err) {
  //     printLog('[saveCartToLocal] failed: $err');
  //   }
  // }
  //
  // {
  //   try {
  //     final newItem = {
  //       'key': key,
  //       'product': product!.toJson(),
  //       'quantity': quantity,
  //       'cartItemMetaData': cartItemMetaData?.toJson(),
  //     };
  //     var items = UserBox().productsInCart;
  //     if (items != null && items.isNotEmpty) {
  //       final existed =
  //           items.firstWhereOrNull((item) => item['key'] == key) != null;
  //       if (existed) {
  //         items =
  //             items.map((item) => item['key'] == key ? newItem : item).toList();
  //       } else {
  //         items.add(newItem);
  //       }
  //     } else {
  //       items = [newItem];
  //     }
  //     UserBox().productsInCart = items;
  //   } catch (err) {
  //     printLog('[saveCartToLocal] failed: $err');
  //   }
  // }

  Future<void> updateQuantityCartLocal(
      {String? key, int quantity = 1, String? size, String? color}) async {
    try {
      printLog('Update Quantity Cart_Local $key - $quantity');
      var items = UserBox().productsInCart;
      if (items != null && items.isNotEmpty) {
        for (var item in items) {
          final productItem = Product.fromLocalJson(item['product']);
          final ids = productItem.id.toString();
          final colorItem = item['color'];
          final sizeItem = item['size'];

          if (productItem.id == ids && color == colorItem && size == sizeItem) {
            item['quantity'] = quantity;
            break;
          }
        }
      }

      UserBox().productsInCart = items;
    } catch (err) {
      printLog(err);
    }
  }

  // Future<void> updateQuantityCartLocal({String? key, int quantity = 1}) async {
  //   try {
  //     printLog('Update Quantity Cart_Local $key - $quantity');
  //     var items = UserBox().productsInCart;
  //     if (items != null && items.isNotEmpty) {
  //       final existed =
  //           items.firstWhereOrNull((item) => item['key'] == key) != null;
  //       if (existed) {
  //         items = items.map((item) {
  //           if (item['key'] == key) {
  //             item['quantity'] = quantity;
  //           }
  //           return item;
  //         }).toList();
  //       }
  //     }
  //     UserBox().productsInCart = items;
  //   } catch (err) {
  //     printLog(err);
  //   }
  // }

  Future<void> clearCartLocal() async {
    UserBox().productsInCart = null;
  }

  void removeProductLocal(String key) {
    try {
      final items = UserBox().productsInCart;
      if (items != null && items.isNotEmpty) {
        final ids = key.split('-');
        var newItems = <Map>[];
        for (var item in items) {
          if (Product.fromLocalJson(item['product']).id != ids[0]) {
            newItems.add(item);
          }
        }
        UserBox().productsInCart = newItems;
      }
    } catch (err) {
      printLog(err);
    }
  }

  void getCartInLocal() {
    if (ServerConfig().isVendorManagerType()) {
      return;
    }
    try {
      final items = UserBox().productsInCart;

      if (items != null && items.isNotEmpty) {
        for (final item in items) {
          addProductToCart(
            product: Product.fromLocalJson(item['product']),
            quantity: item['quantity'],
            cartItemMetaData: item['cartItemMetaData'] != null
                ? CartItemMetaData.fromLocalJson(item['cartItemMetaData'])
                : null,
            isSaveLocal: false,
            notify: () {},
          );
        }
      }
    } catch (err, trace) {
      printError(err, trace, '::::::::: Get Cart In Local Error');
    }
  }

  // Adds a product to the cart.
  String addProductToCart({
    required Product product,
    int? quantity = 1,
    CartItemMetaData? cartItemMetaData,
    required Function notify,
    isSaveLocal = true,
  }) {
    var message = '';

    var key = product.id.toString();
    // if (variation != null) {
    //   if (variation.id != null) {
    //     key += '-${variation.id}';
    //   }
    //
    //   log('afafa ${product.selectedSize}');
    //
    //   if (product.selectedSize != null && product.selectedColor != null) {
    //     key += '-${product.selectedSize}${product.selectedColor}';
    //   }
    //
    //   if (options != null) {
    //     for (var option in options.keys) {
    //       key += '-$option${options[option]}';
    //     }
    //   }
    // }

    //Check product's quantity before adding to cart
    var total = !productsInCart.containsKey(key)
        ? quantity
        : (productsInCart[key]! + quantity!);
    var stockQuantity = product.stockQuantity;
//    printLog('stock is here');
//    printLog(product.manageStock);

    if (!product.manageStock) {
      productsInCart[key] = total;
    } else if (total! <= stockQuantity!) {
      if (product.minQuantity == null && product.maxQuantity == null) {
        productsInCart[key] = total;
      } else if (product.minQuantity != null && product.maxQuantity == null) {
        total < product.minQuantity!
            ? message = 'Minimum quantity is ${product.minQuantity}'
            : productsInCart[key] = total;
      } else if (product.minQuantity == null && product.maxQuantity != null) {
        total > product.maxQuantity!
            ? message =
                'You can only purchase ${product.maxQuantity} for this product'
            : productsInCart[key] = total;
      } else if (product.minQuantity != null && product.maxQuantity != null) {
        if (total >= product.minQuantity! && total <= product.maxQuantity!) {
          productsInCart[key] = total;
        } else {
          if (total < product.minQuantity!) {
            message = 'Minimum quantity is ${product.minQuantity}';
          }
          if (total > product.maxQuantity!) {
            message =
                'You can only purchase ${product.maxQuantity} for this product';
          }
        }
      }
    } else {
      message = 'Currently we only have $stockQuantity of this product';
    }

    if (message.isEmpty) {
      item[product.id] = product;
      // productVariationInCart[key] = variation;
      // productsMetaDataInCart[key] = options;

      if (isSaveLocal) {
        saveCartToLocal(
          key,
          product: product,
          quantity: quantity,
        );
      }
    }

    notify();
    return message;
  }
//   {
//     var message = '';
//
//     var key = product.id.toString();
//     if (cartItemMetaData?.variation != null) {
//       if (cartItemMetaData?.variation?.id != null) {
//         key += '-${cartItemMetaData?.variation?.id}';
//       }
//       if (cartItemMetaData?.options != null) {
//         for (var option in (cartItemMetaData?.options?.keys ?? [])) {
//           key += '-$option${cartItemMetaData?.options?[option]}';
//         }
//       }
//     }
//
//     //Check product's quantity before adding to cart
//     var total = !productsInCart.containsKey(key)
//         ? quantity
//         : (productsInCart[key]! + quantity!);
//     var stockQuantity = cartItemMetaData?.variation == null
//         ? product.stockQuantity
//         : cartItemMetaData?.variation?.stockQuantity;
// //    printLog('stock is here');
// //    printLog(product.manageStock);
//
//     if (!product.manageStock) {
//       productsInCart[key] = total;
//     } else if (total! <= stockQuantity!) {
//       if (product.minQuantity == null && product.maxQuantity == null) {
//         productsInCart[key] = total;
//       } else if (product.minQuantity != null && product.maxQuantity == null) {
//         total < product.minQuantity!
//             ? message = 'Minimum quantity is ${product.minQuantity}'
//             : productsInCart[key] = total;
//       } else if (product.minQuantity == null && product.maxQuantity != null) {
//         total > product.maxQuantity!
//             ? message =
//                 'You can only purchase ${product.maxQuantity} for this product'
//             : productsInCart[key] = total;
//       } else if (product.minQuantity != null && product.maxQuantity != null) {
//         if (total >= product.minQuantity! && total <= product.maxQuantity!) {
//           productsInCart[key] = total;
//         } else {
//           if (total < product.minQuantity!) {
//             message = 'Minimum quantity is ${product.minQuantity}';
//           }
//           if (total > product.maxQuantity!) {
//             message =
//                 'You can only purchase ${product.maxQuantity} for this product';
//           }
//         }
//       }
//     } else {
//       message = 'Currently we only have $stockQuantity of this product';
//     }
//
//     if (message.isEmpty) {
//       item[product.id] = product;
//       cartItemMetaDataInCart[key] = cartItemMetaData;
//
//       if (isSaveLocal) {
//         saveCartToLocal(
//           key,
//           product: product,
//           quantity: quantity,
//           cartItemMetaData: cartItemMetaData,
//         );
//       }
//     }
//
//     notify();
//     return message;
//   }
}
