import 'package:flutter/material.dart';

import '../../../common/tools.dart';
import '../../../data/boxes.dart';
import '../../entities/coupon.dart';
import 'cart_mixin.dart';

mixin CouponMixin on CartMixin implements ChangeNotifier {
  PromoCodeModel? couponObj;
  String? savedCoupon;

  bool _calculatingDiscount = false;

  bool get calculatingDiscount => _calculatingDiscount;

  void setLoadingDiscount() {
    _calculatingDiscount = true;
    notifyListeners();
  }

  void setLoadedDiscount() {
    _calculatingDiscount = false;
    notifyListeners();
  }

  void resetCoupon() {
    couponObj = null;
    clearSavedCoupon();
    notifyListeners();
  }

  Future updateDiscount({PromoCodeModel? discount, Function? onFinish}) async {
    if (discount != null) {
      couponObj = discount;
      savedCoupon = couponObj?.code ?? '';
      return;
    }

    if (couponObj == null) {
      return;
    }

    _calculatingDiscount = true;
    try {
      couponObj = discount;
    } catch (_) {
      resetCoupon();
    } finally {
      _calculatingDiscount = false;
    }

    if (onFinish != null) {
      onFinish();
    }
  }

  String getCoupon() {
    if (couponObj != null) {
      return '-${PriceTools.getCurrencyFormatted(getCouponCost(), currencyRates, currency: currencyCode)}';
    }
    return '';
  }

  num getCouponCost() {
    return couponObj?.discount ?? 0.0;
  }

  void clearSavedCoupon() {
    savedCoupon = null;
    UserBox().savedCoupon = '';
  }
}
