import 'main_media_model.dart';

class TemplateModel {
  final int? id;
  final String name;
  final String url;
  final MainMediaModel? image;

  TemplateModel({this.id, required this.name, this.url = '', this.image});

  String get nameAr {
    if (name == 'Clothes') return 'ملابس';
    if (name == 'Accessories') return 'إكسسوارات';
    if (name == 'digital') return 'رقمي';
    if (name == 'Gifts') return 'هدايا';
    if (name == 'Medical') return 'طبي';
    if (name == 'Market') return 'ماركت';
    if (name == 'Shoes') return 'أحذية';
    if (name == 'Electronics') return 'إلكترونيات';
    if (name == 'General') return 'عام';
    if (name == 'Restaurant') return 'مطعم';
    if (name == 'Default') return 'افتراضي';
    if (name == 'Others') return 'أخرى';
    return name;
  }

  bool get isNotOthers {
    return name != 'Others';
  }

  factory TemplateModel.fromJson(Map<String, dynamic> json) {
    return TemplateModel(
      id: json['id'],
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      image:
          json['image'] != null ? MainMediaModel.fromJson(json['image']) : null,
    );
  }

  factory TemplateModel.empty() => TemplateModel(id: 0, name: '', url: '');
}
