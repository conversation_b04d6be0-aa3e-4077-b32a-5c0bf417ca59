import 'dart:convert';
import 'dart:io' show HttpStatus;

import 'package:inspireui/widgets/coupon_card.dart';
import 'package:provider/provider.dart';

import '../../common/constants.dart';
import '../../generated/l10n.dart';
import '../../services/service_config.dart';
import '../app_model.dart';
import '../cart/cart_base.dart';
import '../order/order.dart';

class Coupons {
  List<Coupon> coupons = [];

  static Future<PromoCodeModel?> getDiscount({
    required CartModel cartModel,
    String? couponCode,
  }) async {
    try {
      final endpoint = '${ServerConfig().url}/wp-json/api/flutter_woo/coupon';
      var params = Order().toJson(cartModel, cartModel.user?.id, false);
      params['coupon_code'] = couponCode;
      final response = await httpPost(endpoint.toUri()!,
          body: json.encode(params),
          headers: {
            'Content-Type': 'application/json',
            'User-Cookie': cartModel.user?.cookie ?? '',
          });
      if (response.statusCode == 502) {
        return null;
      }
      final body = json.decode(response.body) ?? {};
      if (response.statusCode == HttpStatus.ok) {
        if (body['coupon'] != null &&
            body['coupon']['email_restrictions'] != null &&
            body['coupon']['email_restrictions'] is List) {
          if (List.from(body['coupon']['email_restrictions']).isEmpty ||
              List.from(body['coupon']['email_restrictions'])
                  .contains(cartModel.user?.email ?? '')) {
            return PromoCodeModel.fromJson(body);
          }
        } else {
          return PromoCodeModel.fromJson(body);
        }
      } else if (body['message'] != null) {
        throw Exception(body['message']);
      }
    } catch (err) {
      rethrow;
    }
    return null;
  }

  Coupons.getListCoupons(List a) {
    for (var i in a) {
      coupons.add(Coupon.fromJson(i));
    }
  }

  Coupons.getListCouponsOpencart(List a) {
    for (var i in a) {
      coupons.add(Coupon.fromOpencartJson(i));
    }
  }

  Coupons.getListCouponsPresta(List a) {
    for (var i in a) {
      coupons.add(Coupon.fromPresta(i));
    }
  }
}

class PromoCodeModel {
  String code = '';
  int? id;
  num? discount;
  bool isPercent = true;
  bool showInList = true;

  PromoCodeModel(
      {this.code = '',
      this.id,
      this.discount,
      this.isPercent = true,
      this.showInList = true});

  PromoCodeModel.fromJson(Map json) {
    printLog('asfsafassaf ${json}');

    id = json['id'];
    code = json['promo'] ?? '';
    discount = num.parse('${(json['discount'] ?? 0.0)}');
    isPercent = json['is_percent'] ?? true;
    showInList = json['show_in_list'] ?? true;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['promo'] = code;
    data['discount'] = discount;
    data['is_percent'] = isPercent;
    data['show_in_list'] = showInList;
    return data;
  }
}

class CouponTrans extends CouponTranslate {
  CouponTrans(super.context);

  @override
  String get discount => S.of(context).discount;

  @override
  String get expired => S.of(context).expired;

  @override
  String expiringInTime(time) => S.of(context).expiringInTime(time);

  @override
  String get fixedCartDiscount => S.of(context).fixedCartDiscount;

  @override
  String get fixedProductDiscount => S.of(context).fixedProductDiscount;

  @override
  String get langCode => Provider.of<AppModel>(context).langCode;

  @override
  String get saveForLater => S.of(context).saveForLater;

  @override
  String get useNow => S.of(context).useNow;

  @override
  String validUntilDate(data) => S.of(context).validUntilDate(data);
}
