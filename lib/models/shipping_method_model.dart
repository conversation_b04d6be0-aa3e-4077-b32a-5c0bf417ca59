import 'package:flutter/material.dart';

import '../common/config.dart';
import '../models/cart/cart_model.dart';
import '../services/index.dart';
import 'entities/order_delivery_date.dart';
import 'entities/shipping_method.dart';

class ShippingMethodModel extends ChangeNotifier {
  final Services _service = Services();
  List<ShippingMethod>? shippingMethods;
  bool isLoading = true;
  String? message;

  List<OrderDeliveryDate>? _deliveryDates;

  static ValueNotifier<double> selectedShippingMethodCost =
      ValueNotifier<double>(0.0);

  List<OrderDeliveryDate>? get deliveryDates => _deliveryDates;

  Future<void> getShippingMethods(
      {CartModel? cartModel,
      String? token,
      String? checkoutId,
      required String langCode}) async {
    try {
      isLoading = true;
      if (ServerConfig().isOpencart && (shippingMethods?.isNotEmpty ?? false)) {
        shippingMethods = [];
      }
      notifyListeners();
      shippingMethods = await _service.api.getShippingMethods(
        cartModel: cartModel,
        token: token,
        checkoutId: checkoutId,
      );
      if (kAdvanceConfig.enableDeliveryDateOnCheckout) {
        _deliveryDates = await getDelivery();
      }
      isLoading = false;
      message = null;
      notifyListeners();
    } catch (err) {
      isLoading = false;
      message = '⚠️ $err';
      notifyListeners();
    }
  }

  Future<List<OrderDeliveryDate>> getDelivery() async {
    return await _service.api.getListDeliveryDates();
  }
}
