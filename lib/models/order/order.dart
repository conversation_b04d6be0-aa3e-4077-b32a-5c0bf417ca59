import 'package:flutter/material.dart';

import '../../common/config.dart';
import '../../common/constants.dart';
import '../../common/logger.dart';
import '../../common/tools.dart';
import '../../context_extensions.dart';
import '../../data/boxes.dart';
import '../../services/index.dart';
import '../entities/store_delivery_date.dart';
import '../index.dart';
import '../serializers/order.dart';
import 'bank_account_item.dart';
import 'fee_item.dart';
import 'user_location.dart';

export 'delivery_status.dart';
export 'product_item.dart';

// enum OrderStatus {
//   pending,
//   confirmed,
//   isCancelled,
//   delivering,
//   canceled,
//   completed,
//   done,
//   refunded,
//   unknown,
//   processing,
//   cancelled,
//   onHold,
//   failed,
//   //opencart
//   shipped,
//   delivered,
//   reversed,
//   canceledReversal,
//   chargeback,
//   denied,
//   expired,
//   processed,
//   voided,
//   refundRequested,
//   driverAssigned,
//   outForDelivery,
//   orderReturned,
// }

enum OrderStatus {
  pending,
  confirmed,
  delivering,
  canceled,
  done,
  refunded,
  unknown
}

class Order {
  String? id;
  String? orderId;
  String? documentId;
  String? number;
  OrderStatus? status;
  String?
      orderStatus; //in opencart, order_status will be responsed based on language. so I use this property to show on the UI instead of status property if status is unknown
  DateTime? createdAt;
  DateTime? dateModified;
  double? total;
  double? discount;
  double? totalTax;
  double? totalShipping;
  String? paymentMethodTitle;
  String? paymentMethod;
  String? shippingMethodTitle;
  String? customerNote;
  String? customerId;
  int totalProducts = 0;

  // List<(ProductItem product, int productQuantity)> lineItems = [];
  List<(ProductItem product, ProductQuantityModel productQuantity)> lineItems =
      [];

  Address? billing;
  Address? shipping;

  double? subtotal;
  DeliveryStatus? deliveryStatus;
  int quantity = 0;
  UserShippingLocation? userShippingLocation;
  List<AfterShipTracking> aftershipTrackings = [];
  String? deliveryDate;
  List<StoreDeliveryDate>? storeDeliveryDates;
  List<FeeItem> feeLines = [];
  String? currencyCode;
  List<BankAccountItem> bacsInfo = [];
  List<ProductQuantityModel>? products;

  int get totalQuantity {
    var quantity = 0;
    // for (var item in lineItems) {
    //   quantity += item.$1.quantity ?? 0;
    // }
    for (var item in lineItems) {
      quantity += item.$2.quantity ?? 0;
    }
    return quantity;
  }

  Order({
    this.id,
    this.number,
    this.orderId,
    this.status,
    this.createdAt,
    this.total,
    this.discount,
    this.totalProducts = 0,
  });

  factory Order.fromJson(Map<String, dynamic>? parsedJson) {
    return Order._fromStrapiJson(parsedJson!);
  }

  factory Order.fromCreateOrderJson(
    Map<String, dynamic>? parsedJson, {
    int totalProducts = 0,
  }) {
    return Order(
      createdAt: DateTime.parse(parsedJson!['createdAt']?.toString() ?? ''),
      number: parsedJson['order_id']?.toString(),
      orderId: parsedJson['order_id']?.toString(),
      totalProducts: totalProducts,
      id: parsedJson['order_id']?.toString(),
      status: parsedJson['order_status'] == 'canceled'
          ? OrderStatus.canceled
          : OrderStatus.pending,
      total: double.parse(parsedJson['total']?.toString() ?? '0.0'),
      discount: double.parse(parsedJson['discount']?.toString() ?? '0.0'),
    );
  }

  OrderStatus parseOrderStatus(String? status) {
    final newStatus = status?.toLowerCase();
    switch (newStatus) {
      case 'pending':
        return OrderStatus.pending;
      case 'confirmed':
        return OrderStatus.confirmed;
      case 'delivering':
        return OrderStatus.delivering;
      case 'canceled':
        return OrderStatus.canceled;
      case 'done':
        return OrderStatus.done;
      case 'refunded':
        return OrderStatus.refunded;

      default:
        return OrderStatus.values.firstWhere(
          (element) => element.name == newStatus,
          // (element) => describeEnum(element) == newStatus,
          orElse: () => OrderStatus.unknown,
        );
    }
  }

  // OrderStatus parseOrderStatus(String? status) {
  //   final newStatus = status?.toLowerCase();
  //   switch (newStatus) {
  //     case 'canceled':
  //       return OrderStatus.canceled;
  //     case 'complete':
  //       return OrderStatus.done;
  //     case 'pending':
  //       return OrderStatus.pending;
  //     case 'refunded':
  //       return OrderStatus.refunded;
  //     default:
  //       return OrderStatus.values.firstWhere(
  //         (element) => describeEnum(element) == newStatus,
  //         orElse: () => OrderStatus.unknown,
  //       );
  //   }
  // }

  DeliveryStatus parseDeliveryStatus(String? status) {
    final newStatus = status?.toLowerCase();
    return DeliveryStatus.values.firstWhere(
      (element) => element.name == newStatus,
      orElse: () => DeliveryStatus.unknown,
    );
  }

  Order._fromStrapiJson(Map<String, dynamic> parsedJson) {
    try {
      var model = SerializerOrder.fromJson(parsedJson);
      id = model.id.toString();
      orderId = model.orderId.toString();
      documentId = model.documentId.toString();
      number = model.orderId.toString();
      status = parseOrderStatus(parsedJson['order_status'] ?? 'pending');
      discount = model.discount?.toDouble();

      createdAt = DateTime.parse(model.createdAt!);
      paymentMethodTitle = model.payment?.title;
      shippingMethodTitle = model.shipping?.title;
      customerNote = '';
      totalShipping = model.deliveryCost?.toDouble();

      products = model.products;

      total = (model.total ?? 0.0) + (totalShipping ?? 0.0);

      billing = Address.fromStrapiJson(model.address?.toJson() ?? {});

      /// this funciton should be move to framework
      var itemList = model.products ?? [];

      for (var item in itemList) {
        // lineItems.add((
        //   ProductItem.fromStrapiJson(item.serializerProduct!),
        //   item.quantity!
        // ));
        if (item.serializerProduct != null) {
          final productSizeTotalPrice = item.price != null
              ? item.price!
              : (item.serializerProduct!.salePrice ?? 0);

          lineItems.add((
            ProductItem.fromStrapiJson(item.serializerProduct!,
                price: productSizeTotalPrice),
            item
          ));
        }
      }
      totalTax = 0.0;
      subtotal = 0.0;
    } on Exception catch (e, trace) {
      // printLog(e.toString());
      Log.e('$e$trace');
    }
  }

  Order.fromLocalJson(Map parsedJson) {
    id = parsedJson['id'];
    number = parsedJson['number'];
    status = parseOrderStatus(parsedJson['order_status'] ?? 'pending');
    createdAt = parsedJson['createdAt'] != null
        ? DateTime.parse(parsedJson['createdAt'])
        : DateTime.now();
    total =
        parsedJson['total'] != null ? double.parse(parsedJson['total']) : 0.0;
    totalTax = parsedJson['totalTax'] != null
        ? double.parse(parsedJson['totalTax'])
        : 0.0;
    paymentMethodTitle = parsedJson['payment_method_title'];

    parsedJson['line_items']?.forEach((item) {
      lineItems.add((ProductItem.fromLocalJson(item), item['quantity'] ?? 0));
      quantity += int.parse("${item["quantity"]}");
    });

    billing = Address.fromLocalJson(parsedJson['billing']);
    shipping = Address.fromLocalJson(parsedJson['shipping']);
    shippingMethodTitle = parsedJson['shipping_lines'] != null &&
            parsedJson['shipping_lines'].length > 0
        ? parsedJson['shipping_lines'][0]['method_title']
        : null;
  }

  Map<String, dynamic> toOrderJson(CartModel cartModel, userId) {
    var items = lineItems.map((index) {
      return index.$1.toJson();
    }).toList();

    return {
      'order_status': status!.content,
      'total': total.toString(),
      'totalTax': totalTax.toString(),
      'shipping_lines': [
        {'method_title': shippingMethodTitle}
      ],
      'number': number,
      'billing': billing?.toJson(),
      'shipping': shipping?.toJson(),
      'line_items': items,
      'id': id,
      'createdAt': createdAt.toString(),
      'payment_method_title': paymentMethodTitle
    };
  }

  Map<String, dynamic> toJson(CartModel cartModel, userId, paid) {
    var hasAddonsOptions = false;
    var isWalletCart = cartModel.isWalletCart();

    var lineItems = cartModel.productsInCart.keys.map((key) {
      var productId = Product.cleanProductID(key);
      var productVariantId = ProductVariation.cleanProductVariantID(key);

      var product = cartModel.item[productId];
      var itemPrice =
          cartModel.getProductPrice(key) + cartModel.getProductAddonsPrice(key);
      itemPrice = PriceTools.getPriceValueByCurrency(
          itemPrice,
          cartModel.currencyCode ??
              kAdvanceConfig.defaultCurrency?.currencyCode ??
              'USD',
          cartModel.currencyRates ?? {});
      var item = {
        'product_id': productId,
        'quantity': cartModel.productsInCart[key],
      };
      if (cartModel.isIncludingTax != true) {
        item['subtotal'] = '$itemPrice';
        item['total'] = '$itemPrice';
      }
      if (kAdvanceConfig.enableWooCommerceWholesalePrices &&
          ServerConfig().isWooPluginSupported) {
        var loggedInUser = UserBox().userInfo;
        item['meta_data'] = <Map<String, dynamic>>[
          {
            'key': '_wwp_wholesale_priced',
            'value':
                (product?.wholesalePrice?.isNotEmpty ?? false) ? 'yes' : 'no'
          },
          {'key': '_wwp_wholesale_role', 'value': loggedInUser?.role ?? ''}
        ];
      }

      var attrNames = <String?>[];
      if (cartModel.productVariationInCart[key] != null &&
          productVariantId != null) {
        item['variation_id'] = cartModel.productVariationInCart[key]!.id;

        for (var element in cartModel.productVariationInCart[key]!.attributes) {
          if (element.id != null) {
            attrNames.add(element.name);
          }
        }
      }

      if (cartModel.productsMetaDataInCart[key] != null &&
          cartModel.productsMetaDataInCart[key].isNotEmpty) {
        var metaData = <Map<String, dynamic>>[];
        cartModel.productsMetaDataInCart[key].forEach((k, v) {
          if (!attrNames.contains(k)) {
            for (var element in product!.attributes!) {
              if (element.name?.toLowerCase() == k.toString().toLowerCase()) {
                Map<String, dynamic>? option = Map.from(element.options!
                    .firstWhere((e) => e['name'] == v, orElse: () => {}));
                if (option.isNotEmpty) {
                  metaData.add({
                    'key':
                        'attribute_${element.slug ?? option['taxonomy'] ?? element.name}',
                    'value': option['slug'] ?? option['name']
                  });
                }
              }
            }
          }
        });
        item['meta_data'] = metaData;
      }
      if (product!.bookingInfo != null) {
        var metaData = item['meta_data'] as List<Map<String, dynamic>>? ?? [];
        var bookingInfo = product.bookingInfo!.toJsonAPI();
        for (var key in bookingInfo.keys) {
          metaData.add({'key': key, 'value': bookingInfo[key]});
        }
        item['meta_data'] = metaData;
      }

      if (cartModel.productAddonsOptionsInCart[key] != null &&
          cartModel.productAddonsOptionsInCart[key]!.isNotEmpty) {
        hasAddonsOptions = true;
        var metaData = item['meta_data'] as List<Map<String, dynamic>>? ?? [];
        var itemPrice = cartModel.getProductPrice(key);
        final options = cartModel.productAddonsOptionsInCart[key]!;
        var addons = {};

        for (var option in options) {
          //save options to addons to show on the webview
          final fieldName = 'addon-${option.fieldName}';
          var fieldLabel = (option.label ?? '').toLowerCase();
          if (option.type == 'multiple_choice' && option.display == 'select') {
            fieldLabel += '-${(option.index ?? '1')}';
          }
          if (addons[fieldName] == null) {
            addons[fieldName] = fieldLabel;
          } else if (addons[fieldName] is List) {
            addons[fieldName] = [...addons[fieldName], fieldLabel];
          } else {
            addons[fieldName] = [addons[fieldName], fieldLabel];
          }

          final price = PriceTools.getCurrencyFormatted(
              option.price ?? 0.0, cartModel.currencyRates,
              currency: cartModel.currencyCode);
          metaData.add({
            'key':
                "${option.parent}${(option.price?.isNotEmpty ?? false) ? ' ($price)' : ''}",
            'value': option.label,
          });
          itemPrice += (double.tryParse(option.price ?? '0.0') ?? 0) *
              (int.tryParse('${item['quantity']}') ?? 0);
        }

        addons['quantity'] = item['quantity'];
        addons['add-to-cart'] = productId;
        item['addons'] = addons;

        item['subtotal'] = '$itemPrice';
        item['total'] = '$itemPrice';
      }

      if (isWalletCart) {
        var itemPrice = cartModel.getProductPrice(key);
        item['subtotal'] = '$itemPrice';
        item['total'] = '$itemPrice';
      }
      return item;
    }).toList();

    var params = {
      'set_paid': paid,
      'line_items': lineItems,
      'customer_id': userId,
      'currency': cartModel.currencyCode?.toUpperCase(),
    };
    try {
      if (cartModel.paymentMethod != null) {
        params['payment_method'] = cartModel.paymentMethod!.id;
      }
      if (cartModel.paymentMethod != null) {
        params['payment_method_title'] = cartModel.paymentMethod!.title;
      }
      if (paid) params['order_status'] = 'processing';

      if (cartModel.address != null &&
          cartModel.address!.mapUrl != null &&
          cartModel.address!.mapUrl!.isNotEmpty &&
          kPaymentConfig.enableAddressLocationNote) {
        params['customer_note'] = 'URL:${cartModel.address!.mapUrl!}';
      }
      if (kEnableCustomerNote &&
          cartModel.notes != null &&
          cartModel.notes!.isNotEmpty) {
        if (params['customer_note'] != null) {
          params['customer_note'] += '\n${cartModel.notes!}';
        } else {
          params['customer_note'] = cartModel.notes;
        }
      }

      if (kPaymentConfig.enableAddress && cartModel.address != null) {
        params['billing'] = cartModel.address!.toJson();

        params['shipping'] = cartModel.address!.toJson();
        params['billing'].removeWhere((key, value) => value == null);
        params['shipping'].removeWhere((key, value) => value == null);
      }

      var feeLines = [];
      if (cartModel.rewardTotal > 0) {
        feeLines.add({
          'name': 'Cart Discount',
          'tax_status': 'taxable',
          'total': '${cartModel.rewardTotal * (-1)}',
          'amount': '${cartModel.rewardTotal * (-1)}'
        });
      }
      if (cartModel.walletAmount > 0) {
        feeLines.add({
          'name': 'Via Wallet',
          'tax_status': 'taxable',
          'total': '${cartModel.walletAmount * (-1)}',
          'amount': '${cartModel.walletAmount * (-1)}'
        });
        params['total'] = cartModel.getTotal();
      }
      if (cartModel.getCODExtraFee() > 0) {
        feeLines.add({
          'name': 'COD Extra Fee',
          'tax_status': 'taxable',
          'total': '${cartModel.getCODExtraFee()}',
          'amount': '${cartModel.getCODExtraFee()}'
        });
      }
      if (feeLines.isNotEmpty) {
        params['fee_lines'] = feeLines;
      }
      if (cartModel.couponObj != null) {
        params['coupon_lines'] = [
          {'code': cartModel.couponObj!.code}
        ];
      }

      if (hasAddonsOptions ||
          cartModel.couponObj != null ||
          cartModel.walletAmount > 0.0) {
        params['subtotal'] = cartModel.getSubTotal();
        params['total'] = cartModel.getTotal();
      }

      if (kAdvanceConfig.enableDeliveryDateOnCheckout) {
        params['meta_data'] = [];
        if (cartModel.selectedDate != null) {
          params['meta_data'].addAll([
            {
              'key': 'Delivery Date',
              'value': cartModel.selectedDate!.dateString!,
            },
            {
              'key': '_orddd_timestamp',
              'value': cartModel.selectedDate!.timeStamp,
            },
            {
              'key': '_orddd_delivery_schedule_id',
              'value': '0',
            },
          ]);
        }
        if (cartModel.selectedDateByStoreId.isNotEmpty) {
          var value = {};
          cartModel.selectedDateByStoreId.forEach((k, v) {
            value[k] = v.timeStamp;
          });
          params['meta_data']
              .add({'key': '_wcfmd_delvery_times', 'value': value});
        }
      }
    } catch (e, trace) {
      printLog(e.toString());
      printLog(trace.toString());
    }

    return params;
  }

  @override
  String toString() => 'Order { id: $id  number: $number}';
}

extension OrderStatusExtension on OrderStatus {
  static const primaryColor = Color(0xFF25D366);

  bool get isCanceled => [
        OrderStatus.canceled,
        // OrderStatus.canceled,
      ].contains(this);

  String get content => name;

  // describeEnum(this);

  Color get displayColor {
    switch (this) {
      case OrderStatus.pending:
        return Colors.amber;
      case OrderStatus.canceled:
        return Colors.red.withOpacity(0.7);
      case OrderStatus.refunded:
        return Colors.grey;
      case OrderStatus.done || OrderStatus.confirmed:
        return primaryColor;

      default:
        return Colors.yellow;
    }
  }

  String getTranslation(BuildContext context) {
    switch (this) {
      case OrderStatus.pending:
        return context.tr.pending;
      case OrderStatus.canceled:
        return context.tr.canceled;
      case OrderStatus.refunded:
        return context.tr.refunded;
      case OrderStatus.done:
        return context.tr.done;
      case OrderStatus.delivering:
        return context.tr.delivering;
      case OrderStatus.confirmed:
        return context.tr.confirm;
      default:
        return context.tr.failed;
    }
  }
}
