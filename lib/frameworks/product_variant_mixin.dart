import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:inspireui/widgets/disable_widget.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../common/config.dart';
import '../common/constants.dart';
import '../common/logger.dart';
import '../common/tools/flash.dart';
import '../common/tools/tools.dart';
import '../data/boxes.dart';
import '../generated/l10n.dart';
import '../models/cart/cart_item_meta_data.dart';
import '../models/index.dart'
    show
        CartModel,
        Order,
        Product,
        ProductModel,
        ProductVariation,
        ShippingMethodModel,
        UserModel;
import '../models/product_variant_model.dart';
import '../modules/analytics/analytics.dart';
import '../modules/dynamic_layout/helper/helper.dart';
import '../routes/flux_navigate.dart';
import '../screens/cart/cart_screen.dart';
import '../screens/detail/widgets/index.dart' show ProductShortDescription;
import '../screens/products/colors/product_colors_list.dart';
import '../screens/products/sizes/product_sizes_list.dart';
import '../services/services.dart';
import '../widgets/common/webview.dart';
import '../widgets/product/quantity_selection/quantity_selection.dart';
import 'frameworks.dart';
import 'strapi/services/strapi_service.dart';

final Map<String, ValueNotifier<String>> selectedSize =
    <String, ValueNotifier<String>>{};

final selectedColor = <String, ValueNotifier<String>>{}; //? id, value

final selectedDeliveryDateTime = ValueNotifier<DateTime?>(null);

bool isReservationProduct(Product product) {
  return true;
}

mixin ProductVariantMixin on BaseFrameworks {
  ProductVariation? updateVariation(
    List<ProductVariation> variations,
    Map<String?, String?> mapAttribute,
  ) {
    final templateVariation =
        variations.isNotEmpty ? variations.first.attributes : null;
    final listAttributes = variations.map((e) => e.attributes).toList();

    ProductVariation productVariation;
    var attributeString = '';

    /// Flat attribute
    /// Example attribute = { "color": "RED", "SIZE" : "S", "Height": "Short" }
    /// => "colorRedsizeSHeightShort"
    templateVariation?.forEach((element) {
      final key = element.name;
      final value = mapAttribute[key];
      attributeString += value != null ? '$key$value' : '';
    });

    /// Find attributeS contain attribute selected
    final validAttribute = listAttributes.lastWhereOrNull(
      (attributes) =>
          attributeString.contains(attributes.map((e) => e.toString()).join()),
    );

    if (validAttribute == null) return null;

    /// Find ProductVariation contain attribute selected
    /// Compare address because use reference
    productVariation =
        variations.lastWhere((element) => element.attributes == validAttribute);

    for (var element in productVariation.attributes) {
      if (!mapAttribute.containsKey(element.name)) {
        mapAttribute[element.name!] = element.option!;
      }
    }
    return productVariation;
  }

  bool isPurchased(
    ProductVariation? productVariation,
    Product product,
    Map<String?, String?> mapAttribute,
    bool isAvailable,
  ) {
    var inStock;
    if (productVariation != null) {
      inStock = productVariation.inStock!;
    } else {
      inStock = product.inStock!;
    }

    var allowBackorder = productVariation != null
        ? (productVariation.backordersAllowed ?? false)
        : product.backordersAllowed;

    var isValidAttribute = false;
    if (product.type != 'variable') {
      isValidAttribute = true;
    } else if (product.attributes?.length == mapAttribute.length) {
      isValidAttribute = true;
    }

    return (inStock || allowBackorder) && isValidAttribute && isAvailable;
  }

  List<Widget> makeProductTitleWidget(BuildContext context,
      ProductVariation? productVariation, Product product, bool isAvailable) {
    var listWidget = <Widget>[];

    var inStock = productVariation != null
        ? (productVariation.inStock == true &&
            productVariation.stockQuantity != 0)
        : (product.inStock == true && product.stockQuantity != 0);

    var stockQuantity =
        (kProductDetail.showStockQuantity) && product.stockQuantity != null
            ? '  (${product.stockQuantity}) '
            : '';
    if (Provider.of<ProductModel>(context, listen: false).selectedVariation !=
        null) {
      stockQuantity = (kProductDetail.showStockQuantity) &&
              Provider.of<ProductModel>(context, listen: false)
                      .selectedVariation!
                      .stockQuantity !=
                  null
          ? '  (${Provider.of<ProductModel>(context, listen: false).selectedVariation!.stockQuantity}) '
          : '';
    }

    if (isAvailable) {
      listWidget.add(
        const SizedBox(height: 5.0),
      );

      final sku = productVariation != null ? productVariation.sku : product.sku;

      listWidget.add(
        Row(
          children: <Widget>[
            if ((kProductDetail.showSku) && (sku?.isNotEmpty ?? false)) ...[
              Text(
                '${S.of(context).sku}: ',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              Text(
                sku ?? '',
                style: Theme.of(context).textTheme.titleSmall!.copyWith(
                      color: inStock
                          ? Theme.of(context).primaryColor
                          : const Color(0xFFe74c3c),
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ],
        ),
      );

      listWidget.add(
        const SizedBox(height: 5.0),
      );

      // if (currentVendor?.isRestaurant != true) {
      //   listWidget.add(
      //     Row(
      //       children: <Widget>[
      //         if (kProductDetail.showStockStatus) ...[
      //           Text(
      //             '${S.of(context).availability}: ',
      //             style: Theme.of(context).textTheme.titleSmall,
      //           ),
      //           (productVariation != null
      //                   ? (productVariation.backordersAllowed ?? false)
      //                   : product.backordersAllowed)
      //               ? Text(
      //                   S.of(context).backOrder,
      //                   style: Theme.of(context).textTheme.titleSmall!.copyWith(
      //                         color: kStockColor.backorder,
      //                         fontWeight: FontWeight.w600,
      //                       ),
      //                 )
      //               : Text(
      //                   inStock
      //                       ? '${S.of(context).inStock}$stockQuantity'
      //                       : S.of(context).outOfStock,
      //                   style: Theme.of(context).textTheme.titleSmall!.copyWith(
      //                         color: inStock
      //                             ? kStockColor.inStock
      //                             : kStockColor.outOfStock,
      //                         fontWeight: FontWeight.w600,
      //                       ),
      //                 )
      //         ],
      //       ],
      //     ),
      //   );
      // }
      if (productVariation?.description?.isNotEmpty ?? false) {
        listWidget.add(Services()
            .widget
            .renderProductDescription(context, productVariation!.description!));
      }
      if (product.shortDescription != null &&
          product.shortDescription!.isNotEmpty) {
        listWidget.add(
          ProductShortDescription(product),
        );
      }

      listWidget.add(
        const SizedBox(height: 15.0),
      );
    }

    return listWidget;
  }

  List<Widget> makeBuyButtonWidget({
    required BuildContext context,
    ProductVariation? productVariation,
    required Product product,
    Map<String?, String?>? mapAttribute,
    required int maxQuantity,
    required int quantity,
    required Function({bool buyNow, bool inStock}) addToCart,
    required Function(int quantity) onChangeQuantity,
    required bool isAvailable,
    required bool isInAppPurchaseChecking,
    bool showQuantity = true,
    required Function setState,
    Widget Function(bool Function(int) onChanged, int maxQuantity)?
        builderQuantitySelection,
  }) {
    final theme = Theme.of(context);

    final inStock = (productVariation != null
            ? productVariation.inStock
            : product.inStock) ??
        false;

    final allowBackorder = productVariation != null
        ? (productVariation.backordersAllowed ?? false)
        : product.backordersAllowed;

    final allowToBuy = inStock || allowBackorder;

    // External products always only display the buy now button
    final isExternal = product.type == 'external' ? true : false;

    // `configurable` type is used for magento only
    final isVariationLoading =
        (product.isVariableProduct || product.type == 'configurable') &&
            productVariation == null &&
            mapAttribute == null;

    var backgroundColor = theme.primaryColor;

    // If [alwaysShowBuyButton] is true, the buy now button is always displayed
    // instead of buy or out of stock
    final alwaysShowBuyButton = kProductDetail.alwaysShowBuyButton;

    var text = S.of(context).unavailable;

    if (isVariationLoading) {
      text = S.of(context).loading;
    } else if (isInAppPurchaseChecking) {
      text = S.of(context).checking;
    } else if (isExternal ||
        alwaysShowBuyButton ||
        (allowToBuy && isAvailable)) {
      if (isReservationProduct(product)) {
        text = S.of(context).reserveNow;
      } else {
        text = S.of(context).buyNow;
      }
    } else if (!allowToBuy) {
      text = S.of(context).outOfStock;
    }

    final buyOrOutOfStockButton = Container(
      height: 44,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          text.toUpperCase(),
          style: TextStyle(
            color: backgroundColor.getColorBasedOnBackground,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
    );

    final files = product.files ?? [];
    if ((product.isPurchased) &&
        (product.isDownloadable ?? false) &&
        files.isNotEmpty &&
        (files.first?.isNotEmpty ?? false)) {
      return [
        Row(
          children: [
            Expanded(
              child: InkWell(
                //TODO-LaunchURL
                onTap: () async => await Tools.launchURL(files.first,
                    mode: LaunchMode.externalApplication),
                child: Container(
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Center(
                      child: Text(
                    S.of(context).download,
                    style: Theme.of(context).textTheme.labelLarge!.copyWith(
                          color: Colors.white,
                        ),
                  )),
                ),
              ),
            ),
          ],
        ),
      ];
    }

    if (!selectedSize.containsKey(product.id)) {
      selectedSize[product.id] = ValueNotifier('');
    }

    if (!selectedColor.containsKey(product.id)) {
      selectedColor[product.id] = ValueNotifier('');
    }

    final allSizesAndColorsIfExistAndAreOutOfStockOrAllProductIsOutOfStock =
        product.inventoryEnabled
            ? (product.isSizeColorInventory
                ? ((product.colors.isEmpty ||
                        product.colors.every((color) => color.isOutOfStock)) &&
                    (product.sizes.isEmpty ||
                        product.sizes.every((size) => size.isOutOfStock)))
                : product.stockQuantity != null && product.stockQuantity! <= 0)
            : false;

    return [
      PopScope(
        onPopInvoked: (_) {
          setState(() {
            selectedSize.remove(product.id);
            selectedColor.remove(product.id);
          });
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (product.colors.isNotEmpty) ...[
              //? Sizes
              ProductColorsList(
                selectedColor: selectedColor[product.id],
                colors: product.colors,
                setState: setState,
              ),
            ],
            if (product.sizes.isNotEmpty) ...[
              const SizedBox(height: 10),

              //? Sizes
              ProductSizesList(
                selectedSize: selectedSize[product.id],
                sizes: product.sizes,
                setState: setState,
              ),
            ],
            if (isReservationProduct(product)) ...[
              ValueListenableBuilder(
                  valueListenable: selectedDeliveryDateTime,
                  builder: (context, selectedDateTime, child) {
                    final formattedDateTime = selectedDateTime == null
                        ? '-'
                        : DateFormat('d MMM. hh:mm a').format(selectedDateTime);

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            '${S.of(context).deliveryRoute}: $formattedDateTime',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        const SizedBox(width: 15),
                        ElevatedButton(
                          onPressed: () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: selectedDateTime ?? DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );

                            if (picked != null) {
                              final TimeOfDay? timePicked =
                                  await showTimePicker(
                                context: context,
                                initialTime: TimeOfDay.fromDateTime(
                                  selectedDateTime ?? DateTime.now(),
                                ),
                              );

                              if (timePicked != null) {
                                selectedDeliveryDateTime.value = DateTime(
                                  picked.year,
                                  picked.month,
                                  picked.day,
                                  timePicked.hour,
                                  timePicked.minute,
                                );
                              }
                            }
                          },
                          child: Text(
                            S.of(context).pickTime,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
            ],
            if (!isExternal &&
                kProductDetail.showStockQuantity &&
                showQuantity) ...[
              const SizedBox(height: 10),
              if (builderQuantitySelection != null)
                builderQuantitySelection(
                  (p0) => onChangeQuantity(p0),
                  maxQuantity,
                )
              else ...[
                const SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${S.of(context).selectTheQuantity}:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        height: 25,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: QuantitySelection(
                          height: 25,
                          expanded: true,
                          value: quantity,
                          color: theme.colorScheme.secondary,
                          limitSelectQuantity: maxQuantity,
                          style: QuantitySelectionStyle.style01,
                          onChanged: (p0) {
                            final result = onChangeQuantity(p0);

                            return result ?? true;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ],
        ),
      ),

      const SizedBox(height: 20),

      /// Action Buttons: Buy Now and/or Add To Cart
      ///
      if (allSizesAndColorsIfExistAndAreOutOfStockOrAllProductIsOutOfStock) ...[
        const SizedBox(height: 10),
        Text(
          S.of(context).productOutOfStock,
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
        )
      ] else
        actionButton(
          isAvailable,
          addToCart,
          allowToBuy,
          buyOrOutOfStockButton,
          isExternal,
          isVariationLoading,
          isInAppPurchaseChecking,
          context,
          alwaysShowBuyButton: alwaysShowBuyButton,
          product: product,
        )
    ];
  }

  /// Add to Cart & Buy Now function
  @override
  void addToCart(
      BuildContext context, Product product, int quantity, AddToCartArgs? args,
      [bool buyNow = false, bool inStock = false, bool inBackground = false]) {
    final cartModel = Provider.of<CartModel>(context, listen: false);
    if (product.type == 'external') {
      openExternal(context, product);
      return;
    }

    // Variable product but not select all variants.
    if (product.isVariableProduct &&
        product.attributes?.length != args?.mapAttribute?.length) {
      //TODO-ErrorMassage
      FlashHelper.errorMessage(context,
          message: S.of(context).pleaseSelectAllAttributes);
      return;
    }

    // Out of stock
    if (!inStock) {
      FlashHelper.errorMessage(context,
          message: S.of(context).productOutOfStock);
      return;
    }

    final mapAttr = <String, String>{};
    for (var entry in (args?.mapAttribute ?? {}).entries) {
      final key = entry.key;
      final value = entry.value;
      if (key != null && value != null) {
        mapAttr[key] = value;
      }
    }

    var productVariation =
        Provider.of<ProductModel>(context, listen: false).selectedVariation;
    var message = cartModel.addProductToCart(
        context: context,
        product: product,
        quantity: quantity,
        size: selectedSize[product.id]?.value,
        color: selectedColor[product.id]?.value,
        cartItemMetaData: CartItemMetaData(
          variation: productVariation,
          options: mapAttr,
          selectedComponents: args?.selectedComponents,
          selectedTiredPrice: args?.selectedTiredPrice,
          tiredPrices: args?.tiredPrices,
          pwGiftCardInfo: args?.pwGiftCardInfo,
        ));

    if (message.isNotEmpty) {
      FlashHelper.errorMessage(context, message: message);
    } else {
      Analytics.triggerAddToCart(product, quantity, context);
      if (buyNow) {
        FluxNavigate.pushNamed(
          RouteList.cart,
          arguments: CartScreenArgument(isModal: true, isBuyNow: true),
        );
      }
      FlashHelper.message(
        context,
        message: product.name != null
            ? S.of(context).productAddToCart(product.name!)
            : S.of(context).addToCartSucessfully,
        messageStyle: const TextStyle(
          color: Colors.white,
          fontSize: 18.0,
        ),
      );
    }
  }

  /// Support Affiliate product
  @override
  Future<void> openExternal(BuildContext context, Product product) async {
    final url = Tools.prepareURL(product.affiliateUrl);

    if (url != null) {
      try {
        if (kIsWeb ||
            Layout.isDisplayDesktop(context) ||
            Tools.needToOpenExternalApp(url)) {
          await Tools.launchURL(url);
        } else {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => WebView(
                url: product.affiliateUrl,
                title: product.name,
              ),
            ),
          );
        }
        return;
      } catch (e) {
        printError(e);
      }
    }
    await Navigator.push(context, MaterialPageRoute(builder: (context) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Theme.of(context).colorScheme.surface,
          leading: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: const Icon(Icons.arrow_back_ios),
          ),
        ),
        body: Center(
          child: Text(S.of(context).notFound),
        ),
      );
    }));
  }
}

Widget actionButton(
  bool isAvailable,
  Function({bool buyNow, bool inStock}) addToCart,
  bool allowToBuy, // inStock || allowBackorder
  Widget buyOrOutOfStockButton,
  bool isExternal,
  bool isVariationLoading,
  bool isInAppPurchaseChecking,
  BuildContext context, {
  required bool alwaysShowBuyButton,
  required Product product,
}) {
  // Only show grey color filter when cannot buy or add to cart, dont disable
  // the button because it must to show an error message to user via [addToCart]
  // function

  void showReservationDialog() async {
    var isLoggedIn = Provider.of<UserModel>(context, listen: false).loggedIn;

    // if (!isLoggedIn) {
    //   await NavigateTools.navigateToLogin(
    //     context,
    //   );
    //
    //   final user = Provider.of<UserModel>(context, listen: false).user;
    //
    //   if (user != null && user.name != null) {
    //     Tools.showSnackBar(ScaffoldMessenger.of(context),
    //         '${S.of(context).welcome} ${user.name} !');
    //     // setState(() {});
    //   }
    //
    //   return;
    // }

    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    bool isLoading = false;

    // validate selected delivery date time
    if (selectedDeliveryDateTime.value == null) {
      unawaited(FlashHelper.errorMessage(
        context,
        message: S.of(context).pleasePickYourDeliveryDateTime,
      ));
      return;
    }
    var quantity =
        Provider.of<ProductVariantModel>(context, listen: false).quantity;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        final formattedDeliveryDateTime = selectedDeliveryDateTime.value == null
            ? '-'
            : DateFormat('d MMM. hh:mm a')
                .format(selectedDeliveryDateTime.value!);

        Map<String, dynamic> prepareOrderData() {
          // final sizeAndColorAndQuantityBox = UserBox().selectedSizeAndColor;
          // var idShipping = cartModel.shippingMethod?.id;
          // var idPayment = cartModel.paymentMethod?.id;

          final currentVendorId = currentVendor?.id;

          final userModel = Provider.of<UserModel>(context, listen: false);

          var idUser = userModel.user?.id;

          return {
            'total': product.salePrice ?? product.price,
            'user': idUser,
            'products_quantity': [
              {
                'product': product.id,
                'quantity': 1,
                'price': product.salePrice ?? product.price,
                // 'note': 'From: $formattedFirstDate To: $formattedLastDate',
              }
            ],
            'order_status': 'pending',
            'delivery_cost': 0.0,
            // 'address': cartModel.address?.toOrderJson(),
            'phone_number': phoneController.text,
            'guest_name': nameController.text,
            'vendor': currentVendorId,
            'note':
                'Delivery Route: $formattedDeliveryDateTime\nNo. of People: $quantity',
          };
        }

        Future<Order> sendOrderRequest({
          required Map<String, dynamic> data,
        }) async {
          orderId = Random().nextInt(999999);

          data['order_id'] = orderId;

          printLog('asfsafsffsafsfsaf $data');

          var uri = Uri.parse('https://backend.idea2app.tech/api/orders');

          // int? paymentAttachmentId;

          // if (paymentScreenshotAttachment != null) {
          //   paymentAttachmentId =
          //       await uploadFile(fileResult: paymentScreenshotAttachment);
          //   data['payment_attachment'] = paymentAttachmentId;
          // }
          final response = await http.post(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode({'data': data}),
          );

          if (response.statusCode == 200 || response.statusCode == 201) {
            var responseJson = jsonDecode(response.body);

            final docId = responseJson['data']['documentId'];

            await Services().api.connectRelation(data: {
              'orders': {
                'connect': [docId],
              },
            });

            UserBox().clearSelectedSizeAndColor();
            // attachedPaymentScreenshot.value = null;
            // webAttachedPaymentScreenshot.value = null;

            printLog('FGGGGE $responseJson');

            await Services().firebase.sendNotification(
                title: 'You Have New Reservation !',
                body:
                    'Name: ${nameController.text}\nDelivery Route: $formattedDeliveryDateTime',
                userToken: vendorBusinessName.toString(),
                isTopic: true);

            try {
              return Order.fromCreateOrderJson(responseJson['data'],
                  totalProducts: data['products_quantity'].length);
            } catch (e, trace) {
              Log.e('DDDGGGGGGDD$e$trace');

              return Order();
            }
          } else {
            var responseBody = jsonDecode(response.body);
            printLog('FailedRRRRRorder ${response.statusCode} $responseBody');
            throw Exception('Failed to create order');
          }
        }

        Future<void> makeOrder() async {
          //! Make http order
          await sendOrderRequest(
            data: prepareOrderData(),
          );
        }

        return AlertDialog(
          title: Text(S.of(context).reservationDetails),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          content: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${S.of(context).dateTime}: ${selectedDeliveryDateTime.value != null ? DateFormat('d MMM. hh:mm a').format(selectedDeliveryDateTime.value!) : '-'}',
                ),
                Text(
                  '${S.of(context).numberOfPeople}: $quantity',
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: nameController,
                  decoration: InputDecoration(labelText: S.of(context).name),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return S.of(context).nameRequired;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: phoneController,
                  decoration: InputDecoration(labelText: S.of(context).phone),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return S.of(context).phoneRequired;
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(S.of(context).cancel),
            ),
            //TODO-ReservationConfirmationDialog
            ElevatedButton(
              onPressed: isLoading
                  ? null
                  : () async {
                      if (formKey.currentState?.validate() == true &&
                          selectedDeliveryDateTime.value != null) {
                        try {
                          isLoading = true;

                          await makeOrder();
                          Navigator.of(context).pop();

                          unawaited(FlashHelper.message(
                            context,
                            message: S.of(context).reservationSuccess,
                          ));

                          isLoading = false;
                        } catch (e) {
                          isLoading = false;
                          unawaited(FlashHelper.errorMessage(
                            context,
                            message: S.of(context).reservationFailed,
                          ));
                        }
                      } else {
                        isLoading = false;
                        unawaited(FlashHelper.errorMessage(
                          context,
                          message: S.of(context).fillAllFields,
                        ));
                      }
                      isLoading = false;
                    },
              child: Text(S.of(context).confirm),
            ),
          ],
        );
      },
    );
  }

  return DisableWidget(
    showColorFilter: !isAvailable,
    disable: isInAppPurchaseChecking,
    child: Row(
      children: <Widget>[
        //TODO-ReservationButton
        Expanded(
          child: GestureDetector(
            // In the old UX ([alwaysShowBuyButton] is false) and the product is
            // not available, the buy now button (Out of Stock or Unavailable
            // buttons) cannot be clicked
            //
            // External product always can be clicked

            onTap: isReservationProduct(product)
                ? showReservationDialog
                : (alwaysShowBuyButton || isExternal || isAvailable)
                    ? () => addToCart(buyNow: true, inStock: allowToBuy)
                    : null,
            child: buyOrOutOfStockButton,
          ),
        ),

        // Hide add to cart button if it is external or variation is loading
        // if (!isExternal &&
        //     !isVariationLoading &&
        //     !isReservationProduct(product))
        //   // Always show add to cart button if [alwaysShowBuyButton] is true
        //   if (alwaysShowBuyButton || (isAvailable && allowToBuy)) ...[
        //     const SizedBox(width: 10),
        //     Expanded(
        //       child: GestureDetector(
        //         onTap: () => addToCart(buyNow: false, inStock: allowToBuy),
        //         child: Container(
        //           height: 44,
        //           decoration: BoxDecoration(
        //             borderRadius: BorderRadius.circular(3),
        //             color: Theme.of(context).primaryColorLight,
        //           ),
        //           child: Center(
        //             child: Text(
        //               S.of(context).addToCart.toUpperCase(),
        //               style: TextStyle(
        //                 color: Theme.of(context).colorScheme.secondary,
        //                 fontWeight: FontWeight.bold,
        //                 fontSize: 12,
        //               ),
        //             ),
        //           ),
        //         ),
        //       ),
        //     ),
        //   ]
      ],
    ),
  );
}

// import 'dart:async';
//
// import 'package:collection/collection.dart' show IterableExtension;
// import 'package:flutter/material.dart';
// import 'package:inspireui/widgets/disable_widget.dart';
// import 'package:provider/provider.dart';
// import 'package:url_launcher/url_launcher.dart';
//
// import '../common/config.dart';
// import '../common/constants.dart';
// import '../common/tools/flash.dart';
// import '../common/tools/tools.dart';
// import '../generated/l10n.dart';
// import '../models/cart/cart_item_meta_data.dart';
// import '../models/index.dart'
//     show CartModel, Product, ProductModel, ProductVariation;
// import '../modules/analytics/analytics.dart';
// import '../modules/dynamic_layout/helper/helper.dart';
// import '../routes/flux_navigate.dart';
// import '../screens/cart/cart_screen.dart';
// import '../screens/detail/widgets/index.dart' show ProductShortDescription;
// import '../screens/products/colors/product_colors_list.dart';
// import '../screens/products/sizes/product_sizes_list.dart';
// import '../services/services.dart';
// import '../widgets/common/webview.dart';
// import '../widgets/product/quantity_selection/quantity_selection.dart';
// import 'frameworks.dart';
//
// final Map<String, ValueNotifier<String>> selectedSize =
//     <String, ValueNotifier<String>>{};
//
// final selectedColor = <String, ValueNotifier<String>>{}; //? id, value
//
// mixin ProductVariantMixin on BaseFrameworks {
//   ProductVariation? updateVariation(
//     List<ProductVariation> variations,
//     Map<String?, String?> mapAttribute,
//   ) {
//     final templateVariation =
//         variations.isNotEmpty ? variations.first.attributes : null;
//     final listAttributes = variations.map((e) => e.attributes).toList();
//
//     ProductVariation productVariation;
//     var attributeString = '';
//
//     /// Flat attribute
//     /// Example attribute = { "color": "RED", "SIZE" : "S", "Height": "Short" }
//     /// => "colorRedsizeSHeightShort"
//     templateVariation?.forEach((element) {
//       final key = element.name;
//       final value = mapAttribute[key];
//       attributeString += value != null ? '$key$value' : '';
//     });
//
//     /// Find attributeS contain attribute selected
//     final validAttribute = listAttributes.lastWhereOrNull(
//       (attributes) =>
//           attributeString.contains(attributes.map((e) => e.toString()).join()),
//     );
//
//     if (validAttribute == null) return null;
//
//     /// Find ProductVariation contain attribute selected
//     /// Compare address because use reference
//     productVariation =
//         variations.lastWhere((element) => element.attributes == validAttribute);
//
//     for (var element in productVariation.attributes) {
//       if (!mapAttribute.containsKey(element.name)) {
//         mapAttribute[element.name!] = element.option!;
//       }
//     }
//     return productVariation;
//   }
//
//   bool isPurchased(
//     ProductVariation? productVariation,
//     Product product,
//     Map<String?, String?> mapAttribute,
//     bool isAvailable,
//   ) {
//     var inStock;
//     if (productVariation != null) {
//       inStock = productVariation.inStock!;
//     } else {
//       inStock = product.inStock!;
//     }
//
//     var allowBackorder = productVariation != null
//         ? (productVariation.backordersAllowed ?? false)
//         : product.backordersAllowed;
//
//     var isValidAttribute = false;
//     if (product.type != 'variable') {
//       isValidAttribute = true;
//     } else if (product.attributes?.length == mapAttribute.length) {
//       isValidAttribute = true;
//     }
//
//     return (inStock || allowBackorder) && isValidAttribute && isAvailable;
//   }
//
//   List<Widget> makeProductTitleWidget(BuildContext context,
//       ProductVariation? productVariation, Product product, bool isAvailable) {
//     var listWidget = <Widget>[];
//
//     var inStock = productVariation != null
//         ? (productVariation.inStock == true &&
//             productVariation.stockQuantity != 0)
//         : (product.inStock == true && product.stockQuantity != 0);
//
//     var stockQuantity =
//         (kProductDetail.showStockQuantity) && product.stockQuantity != null
//             ? '  (${product.stockQuantity}) '
//             : '';
//     if (Provider.of<ProductModel>(context, listen: false).selectedVariation !=
//         null) {
//       stockQuantity = (kProductDetail.showStockQuantity) &&
//               Provider.of<ProductModel>(context, listen: false)
//                       .selectedVariation!
//                       .stockQuantity !=
//                   null
//           ? '  (${Provider.of<ProductModel>(context, listen: false).selectedVariation!.stockQuantity}) '
//           : '';
//     }
//
//     if (isAvailable) {
//       listWidget.add(
//         const SizedBox(height: 5.0),
//       );
//
//       final sku = productVariation != null ? productVariation.sku : product.sku;
//
//       listWidget.add(
//         Row(
//           children: <Widget>[
//             if ((kProductDetail.showSku) && (sku?.isNotEmpty ?? false)) ...[
//               Text(
//                 '${S.of(context).sku}: ',
//                 style: Theme.of(context).textTheme.titleSmall,
//               ),
//               Text(
//                 sku ?? '',
//                 style: Theme.of(context).textTheme.titleSmall!.copyWith(
//                       color: inStock
//                           ? Theme.of(context).primaryColor
//                           : const Color(0xFFe74c3c),
//                       fontWeight: FontWeight.w600,
//                     ),
//               ),
//             ],
//           ],
//         ),
//       );
//
//       listWidget.add(
//         const SizedBox(height: 5.0),
//       );
//
//       if (currentVendor?.isRestaurant != true) {
//         listWidget.add(
//           Row(
//             children: <Widget>[
//               if (kProductDetail.showStockStatus) ...[
//                 Text(
//                   '${S.of(context).availability}: ',
//                   style: Theme.of(context).textTheme.titleSmall,
//                 ),
//                 (productVariation != null
//                         ? (productVariation.backordersAllowed ?? false)
//                         : product.backordersAllowed)
//                     ? Text(
//                         S.of(context).backOrder,
//                         style: Theme.of(context).textTheme.titleSmall!.copyWith(
//                               color: kStockColor.backorder,
//                               fontWeight: FontWeight.w600,
//                             ),
//                       )
//                     : Text(
//                         inStock
//                             ? '${S.of(context).inStock}$stockQuantity'
//                             : S.of(context).outOfStock,
//                         style: Theme.of(context).textTheme.titleSmall!.copyWith(
//                               color: inStock
//                                   ? kStockColor.inStock
//                                   : kStockColor.outOfStock,
//                               fontWeight: FontWeight.w600,
//                             ),
//                       )
//               ],
//             ],
//           ),
//         );
//       }
//       if (productVariation?.description?.isNotEmpty ?? false) {
//         listWidget.add(Services()
//             .widget
//             .renderProductDescription(context, productVariation!.description!));
//       }
//       if (product.shortDescription != null &&
//           product.shortDescription!.isNotEmpty) {
//         listWidget.add(
//           ProductShortDescription(product),
//         );
//       }
//
//       listWidget.add(
//         const SizedBox(height: 15.0),
//       );
//     }
//
//     return listWidget;
//   }
//
//   List<Widget> makeBuyButtonWidget({
//     required BuildContext context,
//     ProductVariation? productVariation,
//     required Product product,
//     Map<String?, String?>? mapAttribute,
//     required int maxQuantity,
//     required int quantity,
//     required Function({bool buyNow, bool inStock}) addToCart,
//     required Function(int quantity) onChangeQuantity,
//     required bool isAvailable,
//     required bool isInAppPurchaseChecking,
//     bool showQuantity = true,
//     required Function setState,
//     Widget Function(bool Function(int) onChanged, int maxQuantity)?
//         builderQuantitySelection,
//   }) {
//     final theme = Theme.of(context);
//
//     final inStock = (productVariation != null
//             ? productVariation.inStock
//             : product.inStock) ??
//         false;
//
//     final allowBackorder = productVariation != null
//         ? (productVariation.backordersAllowed ?? false)
//         : product.backordersAllowed;
//
//     final allowToBuy = inStock || allowBackorder;
//
//     // External products always only display the buy now button
//     final isExternal = product.type == 'external' ? true : false;
//
//     // `configurable` type is used for magento only
//     final isVariationLoading =
//         (product.isVariableProduct || product.type == 'configurable') &&
//             productVariation == null &&
//             mapAttribute == null;
//
//     var backgroundColor = theme.primaryColor;
//
//     // If [alwaysShowBuyButton] is true, the buy now button is always displayed
//     // instead of buy or out of stock
//     final alwaysShowBuyButton = kProductDetail.alwaysShowBuyButton;
//
//     var text = S.of(context).unavailable;
//
//     if (isVariationLoading) {
//       text = S.of(context).loading;
//     } else if (isInAppPurchaseChecking) {
//       text = S.of(context).checking;
//     } else if (isExternal ||
//         alwaysShowBuyButton ||
//         (allowToBuy && isAvailable)) {
//       text = S.of(context).buyNow;
//     } else if (!allowToBuy) {
//       text = S.of(context).outOfStock;
//     }
//
//     final buyOrOutOfStockButton = Container(
//       height: 44,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(3),
//         color: backgroundColor,
//       ),
//       child: Center(
//         child: Text(
//           text.toUpperCase(),
//           style: TextStyle(
//             color: backgroundColor.getColorBasedOnBackground,
//             fontWeight: FontWeight.bold,
//             fontSize: 12,
//           ),
//         ),
//       ),
//     );
//
//     final files = product.files ?? [];
//     if ((product.isPurchased) &&
//         (product.isDownloadable ?? false) &&
//         files.isNotEmpty &&
//         (files.first?.isNotEmpty ?? false)) {
//       return [
//         Row(
//           children: [
//             Expanded(
//               child: InkWell(
//                 //TODO-LaunchURL
//                 onTap: () async => await Tools.launchURL(files.first,
//                     mode: LaunchMode.externalApplication),
//                 child: Container(
//                   decoration: BoxDecoration(
//                     color: theme.primaryColor,
//                     borderRadius: BorderRadius.circular(5.0),
//                   ),
//                   padding: const EdgeInsets.symmetric(vertical: 16.0),
//                   child: Center(
//                       child: Text(
//                     S.of(context).download,
//                     style: Theme.of(context).textTheme.labelLarge!.copyWith(
//                           color: Colors.white,
//                         ),
//                   )),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ];
//     }
//
//     if (!selectedSize.containsKey(product.id)) {
//       selectedSize[product.id] = ValueNotifier('');
//     }
//
//     if (!selectedColor.containsKey(product.id)) {
//       selectedColor[product.id] = ValueNotifier('');
//     }
//
//     final allSizesAndColorsIfExistAndAreOutOfStockOrAllProductIsOutOfStock =
//         product.inventoryEnabled
//             ? (product.isSizeColorInventory
//                 ? ((product.colors.isEmpty ||
//                         product.colors.every((color) => color.isOutOfStock)) &&
//                     (product.sizes.isEmpty ||
//                         product.sizes.every((size) => size.isOutOfStock)))
//                 : product.stockQuantity != null && product.stockQuantity! <= 0)
//             : false;
//
//     return [
//       PopScope(
//         onPopInvoked: (_) {
//           setState(() {
//             selectedSize.remove(product.id);
//             selectedColor.remove(product.id);
//           });
//         },
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             if (product.colors.isNotEmpty) ...[
//               //? Sizes
//               ProductColorsList(
//                 selectedColor: selectedColor[product.id],
//                 colors: product.colors,
//                 setState: setState,
//               ),
//             ],
//             if (product.sizes.isNotEmpty) ...[
//               const SizedBox(height: 10),
//
//               //? Sizes
//               ProductSizesList(
//                 selectedSize: selectedSize[product.id],
//                 sizes: product.sizes,
//                 setState: setState,
//               ),
//             ],
//             if (!isExternal &&
//                 kProductDetail.showStockQuantity &&
//                 showQuantity) ...[
//               const SizedBox(height: 10),
//               if (builderQuantitySelection != null)
//                 builderQuantitySelection(
//                     (p0) => onChangeQuantity(p0), maxQuantity)
//               else ...[
//                 const SizedBox(height: 10),
//                 Row(
//                   children: [
//                     Expanded(
//                       child: Text(
//                         '${S.of(context).selectTheQuantity}:',
//                         style: Theme.of(context).textTheme.titleMedium,
//                       ),
//                     ),
//                     Expanded(
//                       child: Container(
//                         height: 25,
//                         alignment: Alignment.center,
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(3),
//                         ),
//                         child: QuantitySelection(
//                           height: 25,
//                           expanded: true,
//                           value: quantity,
//                           color: theme.colorScheme.secondary,
//                           limitSelectQuantity: maxQuantity,
//                           style: QuantitySelectionStyle.style01,
//                           onChanged: (p0) {
//                             final result = onChangeQuantity(p0);
//                             return result ?? true;
//                           },
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ],
//           ],
//         ),
//       ),
//
//       const SizedBox(height: 10),
//
//       /// Action Buttons: Buy Now and/or Add To Cart
//       ///
//       if (allSizesAndColorsIfExistAndAreOutOfStockOrAllProductIsOutOfStock) ...[
//         const SizedBox(height: 10),
//         Text(
//           S.of(context).productOutOfStock,
//           style: Theme.of(context).textTheme.titleMedium!.copyWith(
//                 color: Theme.of(context).colorScheme.error,
//               ),
//         )
//       ] else
//         actionButton(
//           isAvailable,
//           addToCart,
//           allowToBuy,
//           buyOrOutOfStockButton,
//           isExternal,
//           isVariationLoading,
//           isInAppPurchaseChecking,
//           context,
//           alwaysShowBuyButton: alwaysShowBuyButton,
//         )
//     ];
//   }
//
//   /// Add to Cart & Buy Now function
//   @override
//   void addToCart(
//       BuildContext context, Product product, int quantity, AddToCartArgs? args,
//       [bool buyNow = false, bool inStock = false, bool inBackground = false]) {
//     final cartModel = Provider.of<CartModel>(context, listen: false);
//     if (product.type == 'external') {
//       openExternal(context, product);
//       return;
//     }
//
//     // Variable product but not select all variants.
//     if (product.isVariableProduct &&
//         product.attributes?.length != args?.mapAttribute?.length) {
//       //TODO-ErrorMassage
//       FlashHelper.errorMessage(context,
//           message: S.of(context).pleaseSelectAllAttributes);
//       return;
//     }
//
//     // Out of stock
//     if (!inStock) {
//       FlashHelper.errorMessage(context,
//           message: S.of(context).productOutOfStock);
//       return;
//     }
//
//     final mapAttr = <String, String>{};
//     for (var entry in (args?.mapAttribute ?? {}).entries) {
//       final key = entry.key;
//       final value = entry.value;
//       if (key != null && value != null) {
//         mapAttr[key] = value;
//       }
//     }
//
//     var productVariation =
//         Provider.of<ProductModel>(context, listen: false).selectedVariation;
//     var message = cartModel.addProductToCart(
//         context: context,
//         product: product,
//         quantity: quantity,
//         size: selectedSize[product.id]?.value,
//         color: selectedColor[product.id]?.value,
//         cartItemMetaData: CartItemMetaData(
//           variation: productVariation,
//           options: mapAttr,
//           selectedComponents: args?.selectedComponents,
//           selectedTiredPrice: args?.selectedTiredPrice,
//           tiredPrices: args?.tiredPrices,
//           pwGiftCardInfo: args?.pwGiftCardInfo,
//         ));
//
//     if (message.isNotEmpty) {
//       FlashHelper.errorMessage(context, message: message);
//     } else {
//       Analytics.triggerAddToCart(product, quantity, context);
//       if (buyNow) {
//         FluxNavigate.pushNamed(
//           RouteList.cart,
//           arguments: CartScreenArgument(isModal: true, isBuyNow: true),
//         );
//       }
//       FlashHelper.message(
//         context,
//         message: product.name != null
//             ? S.of(context).productAddToCart(product.name!)
//             : S.of(context).addToCartSucessfully,
//         messageStyle: const TextStyle(
//           color: Colors.white,
//           fontSize: 18.0,
//         ),
//       );
//     }
//   }
//
//   /// Support Affiliate product
//   @override
//   Future<void> openExternal(BuildContext context, Product product) async {
//     final url = Tools.prepareURL(product.affiliateUrl);
//
//     if (url != null) {
//       try {
//         if (kIsWeb ||
//             Layout.isDisplayDesktop(context) ||
//             Tools.needToOpenExternalApp(url)) {
//           await Tools.launchURL(url);
//         } else {
//           await Navigator.push(
//             context,
//             MaterialPageRoute(
//               builder: (context) => WebView(
//                 url: product.affiliateUrl,
//                 title: product.name,
//               ),
//             ),
//           );
//         }
//         return;
//       } catch (e) {
//         printError(e);
//       }
//     }
//     await Navigator.push(context, MaterialPageRoute(builder: (context) {
//       return Scaffold(
//         appBar: AppBar(
//           backgroundColor: Theme.of(context).colorScheme.surface,
//           leading: GestureDetector(
//             onTap: () {
//               Navigator.pop(context);
//             },
//             child: const Icon(Icons.arrow_back_ios),
//           ),
//         ),
//         body: Center(
//           child: Text(S.of(context).notFound),
//         ),
//       );
//     }));
//   }
// }
//
// Widget actionButton(
//   bool isAvailable,
//   Function({bool buyNow, bool inStock}) addToCart,
//   bool allowToBuy, // inStock || allowBackorder
//   Widget buyOrOutOfStockButton,
//   bool isExternal,
//   bool isVariationLoading,
//   bool isInAppPurchaseChecking,
//   BuildContext context, {
//   required bool alwaysShowBuyButton,
// }) {
//   // Only show grey color filter when cannot buy or add to cart, dont disable
//   // the button because it must to show an error message to user via [addToCart]
//   // function
//   return DisableWidget(
//     showColorFilter: !isAvailable,
//     disable: isInAppPurchaseChecking,
//     child: Row(
//       children: <Widget>[
//         Expanded(
//           child: GestureDetector(
//             // In the old UX ([alwaysShowBuyButton] is false) and the product is
//             // not available, the buy now button (Out of Stock or Unavailable
//             // buttons) cannot be clicked
//             //
//             // External product always can be clicked
//             onTap: (alwaysShowBuyButton || isExternal || isAvailable)
//                 ? () => addToCart(buyNow: true, inStock: allowToBuy)
//                 : null,
//             child: buyOrOutOfStockButton,
//           ),
//         ),
//
//         // Hide add to cart button if it is external or variation is loading
//         if (!isExternal && !isVariationLoading)
//           // Always show add to cart button if [alwaysShowBuyButton] is true
//           if (alwaysShowBuyButton || (isAvailable && allowToBuy)) ...[
//             const SizedBox(width: 10),
//             Expanded(
//               child: GestureDetector(
//                 onTap: () => addToCart(buyNow: false, inStock: allowToBuy),
//                 child: Container(
//                   height: 44,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(3),
//                     color: Theme.of(context).primaryColorLight,
//                   ),
//                   child: Center(
//                     child: Text(
//                       S.of(context).addToCart.toUpperCase(),
//                       style: TextStyle(
//                         color: Theme.of(context).colorScheme.secondary,
//                         fontWeight: FontWeight.bold,
//                         fontSize: 12,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ]
//       ],
//     ),
//   );
// }
