<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="description" content="Explore a wide range of content and enjoy a seamless browsing experience on our platform. Discover now!">
    <meta name="author" content="Content Explorer">
    <meta name="author" content="Idea2App_Customer">
    <meta name="keywords" content="Zafa">
    <meta property="og:title" content="Zafa">
    <meta property="og:description" content="Zafa - Explore Now!">
    <meta property="og:image" content="https://idea2app.com/wp-content/uploads/2021/06/idea2app-logo.png">
    <meta property="og:url" content="https://idea2app.com">
    <meta property="og:site_name" content="Zafa">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Zafa - Explore Now!">
    <meta name="twitter:description" content="Zafa - Explore Now!">
    <meta name="twitter:image" content="https://idea2app.com/wp-content/uploads/2021/06/idea2app-logo.png">
    <link rel="icon" type="image/png" sizes="32x32" href="./favicon.png">
    <link rel="manifest" href="./manifest.json">
    <meta name="theme-color" content="#ffffff">
    <title>Zafa</title>

    <!-- Firebase Configuration -->
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-analytics.js"></script>
    <script src="https://www.gstatic.com/firebasejs/7.20.0/firebase-messaging.js"></script>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCTUQH9tBTBxjAJSpDmDEVllVhWqmR0nR8"></script>

    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #ffffff;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .mini-spinner {
            width: 40px;
            height: 40px;
            display: inline-block;
            position: relative;
        }

        .mini-spinner div {
            transform-origin: 20px 20px;
            animation: spin 1.2s linear infinite;
        }

        .mini-spinner div:after {
            content: " ";
            display: block;
            position: absolute;
            top: 2px;
            left: 18px;
            width: 4px;
            height: 10px;
            border-radius: 20%;
            background: #999;
        }

        .mini-spinner div:nth-child(1) { transform: rotate(0deg); animation-delay: -1.1s; }
        .mini-spinner div:nth-child(2) { transform: rotate(30deg); animation-delay: -1s; }
        .mini-spinner div:nth-child(3) { transform: rotate(60deg); animation-delay: -0.9s; }
        .mini-spinner div:nth-child(4) { transform: rotate(90deg); animation-delay: -0.8s; }
        .mini-spinner div:nth-child(5) { transform: rotate(120deg); animation-delay: -0.7s; }
        .mini-spinner div:nth-child(6) { transform: rotate(150deg); animation-delay: -0.6s; }
        .mini-spinner div:nth-child(7) { transform: rotate(180deg); animation-delay: -0.5s; }
        .mini-spinner div:nth-child(8) { transform: rotate(210deg); animation-delay: -0.4s; }
        .mini-spinner div:nth-child(9) { transform: rotate(240deg); animation-delay: -0.3s; }
        .mini-spinner div:nth-child(10) { transform: rotate(270deg); animation-delay: -0.2s; }
        .mini-spinner div:nth-child(11) { transform: rotate(300deg); animation-delay: -0.1s; }
        .mini-spinner div:nth-child(12) { transform: rotate(330deg); animation-delay: 0s; }

        @keyframes spin {
            0% { opacity: 1; }
            100% { opacity: 0; }
        }
    </style>
</head>
<body>
<div class="mini-spinner">
    <div></div><div></div><div></div><div></div><div></div><div></div>
    <div></div><div></div><div></div><div></div><div></div><div></div>
</div>

<script>
    var serviceWorkerVersion = '{{flutter_service_worker_version}}';
    var scriptLoaded = false;

    function loadMainDartJs() {
        if (scriptLoaded) return;
        scriptLoaded = true;
        var scriptTag = document.createElement('script');
        scriptTag.src = 'main.dart.js';
        scriptTag.type = 'application/javascript';
        document.body.append(scriptTag);
    }

    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function () {
            var serviceWorkerUrl = 'flutter_service_worker.js?v=' + serviceWorkerVersion;
            navigator.serviceWorker.register(serviceWorkerUrl)
                .then((reg) => {
                    function waitForActivation(serviceWorker) {
                        serviceWorker.addEventListener('statechange', () => {
                            if (serviceWorker.state == 'activated') {
                                loadMainDartJs();
                            }
                        });
                    }
                    if (!reg.active && (reg.installing || reg.waiting)) {
                        waitForActivation(reg.installing ?? reg.waiting);
                    } else if (!reg.active.scriptURL.endsWith(serviceWorkerVersion)) {
                        reg.update();
                        waitForActivation(reg.installing);
                    } else {
                        loadMainDartJs();
                    }
                });

            setTimeout(() => {
                if (!scriptLoaded) {
                    loadMainDartJs();
                }
            }, 4000);
        });
    } else {
        loadMainDartJs();
    }
</script>
</body>
</html>
