// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// The name of the route containing the test suite.
const String popupControlsRoute = 'popups';

/// The string supplied to the [ValueKey] for the popup menu button.
const String popupButtonKeyValue = 'PopupControls#PopupButton1';

/// The string supplied to the [ValueKey] for the popup menu.
const String popupKeyValue = 'PopupControls#Popup1';

/// The string supplied to the [ValueKey] for the dropdown button.
const String dropdownButtonKeyValue = 'PopupControls#DropdownButton1';

/// The string supplied to the [ValueKey] for the dropdown button menu.
const String dropdownKeyValue = 'PopupControls#Dropdown1';

/// The string supplied to the [ValueKey] for the alert button.
const String alertButtonKeyValue = 'PopupControls#AlertButton1';

/// The string supplied to the [ValueKey] for the alert dialog.
const String alertKeyValue = 'PopupControls#Alert1';

const List<String> popupItems = <String>['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5'];
