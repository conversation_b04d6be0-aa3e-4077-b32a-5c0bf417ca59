<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the castFrom method from the Converter class, for the Dart programming language.">
  <title>castFrom method - Converter class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/Converter-class.html">Converter<span class="signature">&lt;<wbr><span class="type-parameter">S</span>, <span class="type-parameter">T</span>&gt;</span></a></li>
    <li class="self-crumb">castFrom&lt;<wbr><span class="type-parameter">SS</span>, <span class="type-parameter">ST</span>, <span class="type-parameter">TS</span>, <span class="type-parameter">TT</span>&gt; method</li>
  </ol>
  <div class="self-name">castFrom</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/Converter-class.html">Converter<span class="signature">&lt;<wbr><span class="type-parameter">S</span>, <span class="type-parameter">T</span>&gt;</span></a></li>
      <li class="self-crumb">castFrom&lt;<wbr><span class="type-parameter">SS</span>, <span class="type-parameter">ST</span>, <span class="type-parameter">TS</span>, <span class="type-parameter">TT</span>&gt; method</li>
    </ol>
    
    <h5>Converter class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-convert/Converter-class.html#constructors">Constructors</a></li>
        <li><a href="dart-convert/Converter/Converter.html">Converter</a></li>
    
        <li class="section-title inherited">
            <a href="dart-convert/Converter-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-convert/Converter-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-convert/Converter/bind.html">bind</a></li>
        <li><a href="dart-convert/Converter/cast.html">cast</a></li>
        <li><a href="dart-convert/Converter/convert.html">convert</a></li>
        <li><a href="dart-convert/Converter/fuse.html">fuse</a></li>
        <li><a href="dart-convert/Converter/startChunkedConversion.html">startChunkedConversion</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-convert/Converter-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-convert/Converter-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-convert/Converter/castFrom.html">castFrom</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">castFrom&lt;<wbr><span class="type-parameter">SS</span>, <span class="type-parameter">ST</span>, <span class="type-parameter">TS</span>, <span class="type-parameter">TT</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-convert/Converter-class.html">Converter</a><span class="signature">&lt;<wbr><span class="type-parameter">TS</span>, <span class="type-parameter">TT</span>&gt;</span></span>
            <span class="name ">castFrom</span>
&lt;<wbr><span class="type-parameter">SS</span>, <span class="type-parameter">ST</span>, <span class="type-parameter">TS</span>, <span class="type-parameter">TT</span>&gt;(<wbr><span class="parameter" id="castFrom-param-source"><span class="type-annotation"><a href="dart-convert/Converter-class.html">Converter</a><span class="signature">&lt;<wbr><span class="type-parameter">SS</span>, <span class="type-parameter">ST</span>&gt;</span></span> <span class="parameter-name">source</span></span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Adapts <code>source</code> to be a <code>Converter&lt;TS, TT&gt;</code>.</p>
<p>This allows <code>source</code> to be used at the new type, but at run-time it
must satisfy the requirements of both the new type and its original type.</p>
<p>Conversion input must be both <code>SS</code> and <code>TS</code> and the output created by
<code>source</code> for those input must be both <code>ST</code> and <code>TT</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static Converter&lt;TS, TT&gt; castFrom&lt;SS, ST, TS, TT&gt;(Converter&lt;SS, ST&gt; source) =&gt;
    CastConverter&lt;SS, ST, TS, TT&gt;(source);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
