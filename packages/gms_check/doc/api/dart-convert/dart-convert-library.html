<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="dart:convert library API docs, for the Dart programming language.">
  <title>dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li class="self-crumb">dart:convert library</li>
  </ol>
  <div class="self-name">dart:convert</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li class="self-crumb">dart:convert library</li>
    </ol>
    
    <h5><span class="package-name">gms_check</span> <span class="package-kind">package</span></h5>
    <ol>
          <li class="section-title">Libraries</li>
          <li><a href="gms_check/gms_check-library.html">gms_check</a></li>
          <li class="section-title">Dart</li>
          <li><a href="dart-ui/dart-ui-library.html">dart:ui</a></li>
          <li class="section-subtitle">Core</li>
            <li class="section-subitem"><a href="dart-async/dart-async-library.html">dart:async</a></li>
            <li class="section-subitem"><a href="dart-collection/dart-collection-library.html">dart:collection</a></li>
            <li class="section-subitem"><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
            <li class="section-subitem"><a href="dart-core/dart-core-library.html">dart:core</a></li>
            <li class="section-subitem"><a href="dart-developer/dart-developer-library.html">dart:developer</a></li>
            <li class="section-subitem"><a href="dart-math/dart-math-library.html">dart:math</a></li>
            <li class="section-subitem"><a href="dart-typed_data/dart-typed_data-library.html">dart:typed_data</a></li>
          <li class="section-subtitle">VM</li>
            <li class="section-subitem"><a href="dart-ffi/dart-ffi-library.html">dart:ffi</a></li>
            <li class="section-subitem"><a href="dart-io/dart-io-library.html">dart:io</a></li>
            <li class="section-subitem"><a href="dart-isolate/dart-isolate-library.html">dart:isolate</a></li>
          <li class="section-subtitle">Web</li>
            <li class="section-subitem"><a href="dart-html/dart-html-library.html">dart:html</a></li>
            <li class="section-subitem"><a href="dart-js/dart-js-library.html">dart:js</a></li>
            <li class="section-subitem"><a href="dart-js_util/dart-js_util-library.html">dart:js_util</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-library">dart:convert</span> library </h1></div>

    <section class="desc markdown">
      <p>Encoders and decoders for converting between different data representations,
including JSON and UTF-8.</p>
<p>In addition to converters for common data representations, this library
provides support for implementing converters in a way which makes them easy to
chain and to use with streams.</p>
<p>To use this library in your code:</p>
<pre class="language-dart"><code class="language-dart">import 'dart:convert';
</code></pre>
<p>Two commonly used converters are the top-level instances of
<a href="dart-convert/JsonCodec-class.html">JsonCodec</a> and <a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a>, named <a href="dart-convert/json-constant.html">json</a> and <a href="dart-convert/utf8-constant.html">utf8</a>, respectively.</p>
<p>JSON is a simple text format for representing
structured objects and collections.
The JSON encoder/decoder transforms between strings and
object structures, such as lists and maps, using the JSON format.</p>
<p>UTF-8 is a common variable-width encoding that can represent
every character in the Unicode character set.
The UTF-8 encoder/decoder transforms between Strings and bytes.</p>
<p>Converters are often used with streams
to transform the data that comes through the stream
as it becomes available.
The following code uses two converters.
The first is a UTF-8 decoder, which converts the data from bytes to UTF-8
as it's read from a file,
The second is an instance of <a href="dart-convert/LineSplitter-class.html">LineSplitter</a>,
which splits the data on newline boundaries.</p>
<pre class="language-dart"><code class="language-dart">var lineNumber = 1;
var stream = File('quotes.txt').openRead();

stream.transform(utf8.decoder)
      .transform(const LineSplitter())
      .listen((line) {
        if (showLineNumbers) {
          stdout.write('${lineNumber++} ');
        }
        stdout.writeln(line);
      });
</code></pre>
<p>See the documentation for the <a href="dart-convert/Codec-class.html">Codec</a> and <a href="dart-convert/Converter-class.html">Converter</a> classes
for information about creating your own converters.</p>
    </section>
    
    <section class="summary offset-anchor" id="classes">
      <h2>Classes</h2>

      <dl>
        <dt id="AsciiCodec">
          <span class="name "><a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a></span>         
        </dt>
        <dd>
          An <a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a> allows encoding strings as ASCII bytes
and decoding ASCII bytes to strings.
        </dd>
        <dt id="AsciiDecoder">
          <span class="name "><a href="dart-convert/AsciiDecoder-class.html">AsciiDecoder</a></span>         
        </dt>
        <dd>
          
        </dd>
        <dt id="AsciiEncoder">
          <span class="name "><a href="dart-convert/AsciiEncoder-class.html">AsciiEncoder</a></span>         
        </dt>
        <dd>
          This class converts strings of only ASCII characters to bytes.
        </dd>
        <dt id="Base64Codec">
          <span class="name "><a href="dart-convert/Base64Codec-class.html">Base64Codec</a></span>         
        </dt>
        <dd>
          A <a href="https://tools.ietf.org/html/rfc4648">base64</a> encoder and decoder. <a href="dart-convert/Base64Codec-class.html">[...]</a>
        </dd>
        <dt id="Base64Decoder">
          <span class="name "><a href="dart-convert/Base64Decoder-class.html">Base64Decoder</a></span>         
        </dt>
        <dd>
          Decoder for base64 encoded data. <a href="dart-convert/Base64Decoder-class.html">[...]</a>
        </dd>
        <dt id="Base64Encoder">
          <span class="name "><a href="dart-convert/Base64Encoder-class.html">Base64Encoder</a></span>         
        </dt>
        <dd>
          Base64 and base64url encoding converter. <a href="dart-convert/Base64Encoder-class.html">[...]</a>
        </dd>
        <dt id="ByteConversionSink">
          <span class="name "><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></span>         
        </dt>
        <dd>
          The <a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a> provides an interface for converters to
efficiently transmit byte data. <a href="dart-convert/ByteConversionSink-class.html">[...]</a>
        </dd>
        <dt id="ByteConversionSinkBase">
          <span class="name "><a href="dart-convert/ByteConversionSinkBase-class.html">ByteConversionSinkBase</a></span>         
        </dt>
        <dd>
          This class provides a base-class for converters that need to accept byte
inputs.
        </dd>
        <dt id="ChunkedConversionSink">
          <span class="name "><a href="dart-convert/ChunkedConversionSink-class.html">ChunkedConversionSink</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-convert/ChunkedConversionSink-class.html">ChunkedConversionSink</a> is used to transmit data more efficiently between
two converters during chunked conversions. <a href="dart-convert/ChunkedConversionSink-class.html">[...]</a>
        </dd>
        <dt id="ClosableStringSink">
          <span class="name "><a href="dart-convert/ClosableStringSink-class.html">ClosableStringSink</a></span>         
        </dt>
        <dd>
          A <a href="dart-convert/ClosableStringSink-class.html">ClosableStringSink</a> extends the <a href="dart-core/StringSink-class.html">StringSink</a> interface by adding a
<code>close</code> method.
        </dd>
        <dt id="Codec">
          <span class="name "><a href="dart-convert/Codec-class.html">Codec</a><span class="signature">&lt;<wbr><span class="type-parameter">S</span>, <span class="type-parameter">T</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-convert/Codec-class.html">Codec</a> encodes and (if supported) decodes data. <a href="dart-convert/Codec-class.html">[...]</a>
        </dd>
        <dt id="Converter">
          <span class="name "><a href="dart-convert/Converter-class.html">Converter</a><span class="signature">&lt;<wbr><span class="type-parameter">S</span>, <span class="type-parameter">T</span>&gt;</span></span>         
        </dt>
        <dd>
          A <a href="dart-convert/Converter-class.html">Converter</a> converts data from one representation into another. <a href="dart-convert/Converter-class.html">[...]</a>
        </dd>
        <dt id="Encoding">
          <span class="name "><a href="dart-convert/Encoding-class.html">Encoding</a></span>         
        </dt>
        <dd>
          Open-ended Encoding enum.
        </dd>
        <dt id="HtmlEscape">
          <span class="name "><a href="dart-convert/HtmlEscape-class.html">HtmlEscape</a></span>         
        </dt>
        <dd>
          Converter which escapes characters with special meaning in HTML. <a href="dart-convert/HtmlEscape-class.html">[...]</a>
        </dd>
        <dt id="HtmlEscapeMode">
          <span class="name "><a href="dart-convert/HtmlEscapeMode-class.html">HtmlEscapeMode</a></span>         
        </dt>
        <dd>
          HTML escape modes. <a href="dart-convert/HtmlEscapeMode-class.html">[...]</a>
        </dd>
        <dt id="JsonCodec">
          <span class="name "><a href="dart-convert/JsonCodec-class.html">JsonCodec</a></span>         
        </dt>
        <dd>
          A <a href="dart-convert/JsonCodec-class.html">JsonCodec</a> encodes JSON objects to strings and decodes strings to
JSON objects. <a href="dart-convert/JsonCodec-class.html">[...]</a>
        </dd>
        <dt id="JsonDecoder">
          <span class="name "><a href="dart-convert/JsonDecoder-class.html">JsonDecoder</a></span>         
        </dt>
        <dd>
          This class parses JSON strings and builds the corresponding objects. <a href="dart-convert/JsonDecoder-class.html">[...]</a>
        </dd>
        <dt id="JsonEncoder">
          <span class="name "><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></span>         
        </dt>
        <dd>
          This class converts JSON objects to strings.
        </dd>
        <dt id="JsonUtf8Encoder">
          <span class="name "><a href="dart-convert/JsonUtf8Encoder-class.html">JsonUtf8Encoder</a></span>         
        </dt>
        <dd>
          Encoder that encodes a single object as a UTF-8 encoded JSON string. <a href="dart-convert/JsonUtf8Encoder-class.html">[...]</a>
        </dd>
        <dt id="Latin1Codec">
          <span class="name "><a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a></span>         
        </dt>
        <dd>
          A <a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a> encodes strings to ISO Latin-1 (aka ISO-8859-1) bytes
and decodes Latin-1 bytes to strings.
        </dd>
        <dt id="Latin1Decoder">
          <span class="name "><a href="dart-convert/Latin1Decoder-class.html">Latin1Decoder</a></span>         
        </dt>
        <dd>
          This class converts Latin-1 bytes (lists of unsigned 8-bit integers)
to a string.
        </dd>
        <dt id="Latin1Encoder">
          <span class="name "><a href="dart-convert/Latin1Encoder-class.html">Latin1Encoder</a></span>         
        </dt>
        <dd>
          This class converts strings of only ISO Latin-1 characters to bytes.
        </dd>
        <dt id="LineSplitter">
          <span class="name "><a href="dart-convert/LineSplitter-class.html">LineSplitter</a></span>         
        </dt>
        <dd>
          A <a href="dart-async/StreamTransformer-class.html">StreamTransformer</a> that splits a <a href="dart-core/String-class.html">String</a> into individual lines. <a href="dart-convert/LineSplitter-class.html">[...]</a>
        </dd>
        <dt id="StringConversionSink">
          <span class="name "><a href="dart-convert/StringConversionSink-class.html">StringConversionSink</a></span>         
        </dt>
        <dd>
          This class provides an interface for converters to
efficiently transmit String data. <a href="dart-convert/StringConversionSink-class.html">[...]</a>
        </dd>
        <dt id="StringConversionSinkBase">
          <span class="name "><a href="dart-convert/StringConversionSinkBase-class.html">StringConversionSinkBase</a></span>         
        </dt>
        <dd>
          This class provides a base-class for converters that need to accept String
inputs.
        </dd>
        <dt id="StringConversionSinkMixin">
          <span class="name "><a href="dart-convert/StringConversionSinkMixin-class.html">StringConversionSinkMixin</a></span>         
        </dt>
        <dd>
          This class provides a mixin for converters that need to accept String
inputs.
        </dd>
        <dt id="Utf8Codec">
          <span class="name "><a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a></span>         
        </dt>
        <dd>
          A <a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a> encodes strings to utf-8 code units (bytes) and decodes
UTF-8 code units to strings.
        </dd>
        <dt id="Utf8Decoder">
          <span class="name "><a href="dart-convert/Utf8Decoder-class.html">Utf8Decoder</a></span>         
        </dt>
        <dd>
          This class converts UTF-8 code units (lists of unsigned 8-bit integers)
to a string.
        </dd>
        <dt id="Utf8Encoder">
          <span class="name "><a href="dart-convert/Utf8Encoder-class.html">Utf8Encoder</a></span>         
        </dt>
        <dd>
          This class converts strings to their UTF-8 code units (a list of
unsigned 8-bit integers).
        </dd>
      </dl>
    </section>



    <section class="summary offset-anchor" id="constants">
      <h2>Constants</h2>

      <dl class="properties">
        <dt id="ascii" class="constant">
          <span class="name "><a href="dart-convert/ascii-constant.html">ascii</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a></span>
                  </dt>
        <dd>
          An instance of the default implementation of the <a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a>. <a href="dart-convert/ascii-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const <a href="dart-convert/AsciiCodec/AsciiCodec.html">AsciiCodec</a>()</code></span>
          </div>
        </dd>
        <dt id="base64" class="constant">
          <span class="name "><a href="dart-convert/base64-constant.html">base64</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/Base64Codec-class.html">Base64Codec</a></span>
                  </dt>
        <dd>
          A <a href="https://tools.ietf.org/html/rfc4648">base64</a> encoder and decoder. <a href="dart-convert/base64-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const <a href="dart-convert/Base64Codec/Base64Codec.html">Base64Codec</a>()</code></span>
          </div>
        </dd>
        <dt id="base64Url" class="constant">
          <span class="name "><a href="dart-convert/base64Url-constant.html">base64Url</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/Base64Codec-class.html">Base64Codec</a></span>
                  </dt>
        <dd>
          A <a href="https://tools.ietf.org/html/rfc4648">base64url</a> encoder and decoder. <a href="dart-convert/base64Url-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const Base64Codec.urlSafe()</code></span>
          </div>
        </dd>
        <dt id="htmlEscape" class="constant">
          <span class="name "><a href="dart-convert/htmlEscape-constant.html">htmlEscape</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/HtmlEscape-class.html">HtmlEscape</a></span>
                  </dt>
        <dd>
          A <code>String</code> converter that converts characters to HTML entities. <a href="dart-convert/htmlEscape-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const <a href="dart-convert/HtmlEscape/HtmlEscape.html">HtmlEscape</a>()</code></span>
          </div>
        </dd>
        <dt id="json" class="constant">
          <span class="name "><a href="dart-convert/json-constant.html">json</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/JsonCodec-class.html">JsonCodec</a></span>
                  </dt>
        <dd>
          An instance of the default implementation of the <a href="dart-convert/JsonCodec-class.html">JsonCodec</a>. <a href="dart-convert/json-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const <a href="dart-convert/JsonCodec/JsonCodec.html">JsonCodec</a>()</code></span>
          </div>
        </dd>
        <dt id="latin1" class="constant">
          <span class="name "><a href="dart-convert/latin1-constant.html">latin1</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a></span>
                  </dt>
        <dd>
          An instance of the default implementation of the <a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a>. <a href="dart-convert/latin1-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const <a href="dart-convert/Latin1Codec/Latin1Codec.html">Latin1Codec</a>()</code></span>
          </div>
        </dd>
        <dt id="unicodeBomCharacterRune" class="constant">
          <span class="name "><a href="dart-convert/unicodeBomCharacterRune-constant.html">unicodeBomCharacterRune</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          The Unicode Byte Order Marker (BOM) character <code>U+FEFF</code>.
                  
  <div>
            <span class="signature"><code>0xFEFF</code></span>
          </div>
        </dd>
        <dt id="unicodeReplacementCharacterRune" class="constant">
          <span class="name "><a href="dart-convert/unicodeReplacementCharacterRune-constant.html">unicodeReplacementCharacterRune</a></span>
          <span class="signature">&#8594; const <a href="dart-core/int-class.html">int</a></span>
                  </dt>
        <dd>
          The Unicode Replacement character <code>U+FFFD</code> (�).
                  
  <div>
            <span class="signature"><code>0xFFFD</code></span>
          </div>
        </dd>
        <dt id="utf8" class="constant">
          <span class="name "><a href="dart-convert/utf8-constant.html">utf8</a></span>
          <span class="signature">&#8594; const <a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a></span>
                  </dt>
        <dd>
          An instance of the default implementation of the <a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a>. <a href="dart-convert/utf8-constant.html">[...]</a>
                  
  <div>
            <span class="signature"><code>const <a href="dart-convert/Utf8Codec/Utf8Codec.html">Utf8Codec</a>()</code></span>
          </div>
        </dd>
      </dl>
    </section>


    <section class="summary offset-anchor" id="functions">
      <h2>Functions</h2>

      <dl class="callables">
        <dt id="base64Decode" class="callable">
          <span class="name"><a href="dart-convert/base64Decode.html">base64Decode</a></span><span class="signature">(<wbr><span class="parameter" id="base64Decode-param-source"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">source</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-typed_data/Uint8List-class.html">Uint8List</a></span>
          </span>
                  </dt>
        <dd>
          Decodes <a href="https://tools.ietf.org/html/rfc4648">base64</a> or <a href="https://tools.ietf.org/html/rfc4648">base64url</a> encoded bytes. <a href="dart-convert/base64Decode.html">[...]</a>
                  
</dd>
        <dt id="base64Encode" class="callable">
          <span class="name"><a href="dart-convert/base64Encode.html">base64Encode</a></span><span class="signature">(<wbr><span class="parameter" id="base64Encode-param-bytes"><span class="type-annotation"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/int-class.html">int</a></span>&gt;</span></span> <span class="parameter-name">bytes</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          Encodes <code>bytes</code> using <a href="https://tools.ietf.org/html/rfc4648">base64</a> encoding. <a href="dart-convert/base64Encode.html">[...]</a>
                  
</dd>
        <dt id="base64UrlEncode" class="callable">
          <span class="name"><a href="dart-convert/base64UrlEncode.html">base64UrlEncode</a></span><span class="signature">(<wbr><span class="parameter" id="base64UrlEncode-param-bytes"><span class="type-annotation"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/int-class.html">int</a></span>&gt;</span></span> <span class="parameter-name">bytes</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          Encodes <code>bytes</code> using <a href="https://tools.ietf.org/html/rfc4648">base64url</a> encoding. <a href="dart-convert/base64UrlEncode.html">[...]</a>
                  
</dd>
        <dt id="jsonDecode" class="callable">
          <span class="name"><a href="dart-convert/jsonDecode.html">jsonDecode</a></span><span class="signature">(<wbr><span class="parameter" id="jsonDecode-param-source"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">source</span>, {</span> <span class="parameter" id="jsonDecode-param-reviver"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">reviver</span>(<span class="parameter" id="param-key"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">key</span></span> <span class="parameter" id="param-value"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">value</span></span>)</span> })
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd>
          Parses the string and returns the resulting Json object. <a href="dart-convert/jsonDecode.html">[...]</a>
                  
</dd>
        <dt id="jsonEncode" class="callable">
          <span class="name"><a href="dart-convert/jsonEncode.html">jsonEncode</a></span><span class="signature">(<wbr><span class="parameter" id="jsonEncode-param-object"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">object</span>, {</span> <span class="parameter" id="jsonEncode-param-toEncodable"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">toEncodable</span>(<span class="parameter" id="param-nonEncodable"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">nonEncodable</span></span>)</span> })
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd>
          Converts <code>object</code> to a JSON string. <a href="dart-convert/jsonEncode.html">[...]</a>
                  
</dd>
      </dl>
    </section>



    <section class="summary offset-anchor" id="exceptions">
      <h2>Exceptions / Errors</h2>

      <dl>
        <dt id="JsonCyclicError">
          <span class="name "><a href="dart-convert/JsonCyclicError-class.html">JsonCyclicError</a></span>         
        </dt>
        <dd>
          Reports that an object could not be stringified due to cyclic references. <a href="dart-convert/JsonCyclicError-class.html">[...]</a>
        </dd>
        <dt id="JsonUnsupportedObjectError">
          <span class="name "><a href="dart-convert/JsonUnsupportedObjectError-class.html">JsonUnsupportedObjectError</a></span>         
        </dt>
        <dd>
          Error thrown by JSON serialization if an object cannot be serialized. <a href="dart-convert/JsonUnsupportedObjectError-class.html">[...]</a>
        </dd>
      </dl>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <h5>dart:convert library</h5>
    <ol>
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#classes">Classes</a></li>
      <li><a href="dart-convert/AsciiCodec-class.html">AsciiCodec</a></li>
      <li><a href="dart-convert/AsciiDecoder-class.html">AsciiDecoder</a></li>
      <li><a href="dart-convert/AsciiEncoder-class.html">AsciiEncoder</a></li>
      <li><a href="dart-convert/Base64Codec-class.html">Base64Codec</a></li>
      <li><a href="dart-convert/Base64Decoder-class.html">Base64Decoder</a></li>
      <li><a href="dart-convert/Base64Encoder-class.html">Base64Encoder</a></li>
      <li><a href="dart-convert/ByteConversionSink-class.html">ByteConversionSink</a></li>
      <li><a href="dart-convert/ByteConversionSinkBase-class.html">ByteConversionSinkBase</a></li>
      <li><a href="dart-convert/ChunkedConversionSink-class.html">ChunkedConversionSink</a></li>
      <li><a href="dart-convert/ClosableStringSink-class.html">ClosableStringSink</a></li>
      <li><a href="dart-convert/Codec-class.html">Codec</a></li>
      <li><a href="dart-convert/Converter-class.html">Converter</a></li>
      <li><a href="dart-convert/Encoding-class.html">Encoding</a></li>
      <li><a href="dart-convert/HtmlEscape-class.html">HtmlEscape</a></li>
      <li><a href="dart-convert/HtmlEscapeMode-class.html">HtmlEscapeMode</a></li>
      <li><a href="dart-convert/JsonCodec-class.html">JsonCodec</a></li>
      <li><a href="dart-convert/JsonDecoder-class.html">JsonDecoder</a></li>
      <li><a href="dart-convert/JsonEncoder-class.html">JsonEncoder</a></li>
      <li><a href="dart-convert/JsonUtf8Encoder-class.html">JsonUtf8Encoder</a></li>
      <li><a href="dart-convert/Latin1Codec-class.html">Latin1Codec</a></li>
      <li><a href="dart-convert/Latin1Decoder-class.html">Latin1Decoder</a></li>
      <li><a href="dart-convert/Latin1Encoder-class.html">Latin1Encoder</a></li>
      <li><a href="dart-convert/LineSplitter-class.html">LineSplitter</a></li>
      <li><a href="dart-convert/StringConversionSink-class.html">StringConversionSink</a></li>
      <li><a href="dart-convert/StringConversionSinkBase-class.html">StringConversionSinkBase</a></li>
      <li><a href="dart-convert/StringConversionSinkMixin-class.html">StringConversionSinkMixin</a></li>
      <li><a href="dart-convert/Utf8Codec-class.html">Utf8Codec</a></li>
      <li><a href="dart-convert/Utf8Decoder-class.html">Utf8Decoder</a></li>
      <li><a href="dart-convert/Utf8Encoder-class.html">Utf8Encoder</a></li>
    
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#constants">Constants</a></li>
      <li><a href="dart-convert/ascii-constant.html">ascii</a></li>
      <li><a href="dart-convert/base64-constant.html">base64</a></li>
      <li><a href="dart-convert/base64Url-constant.html">base64Url</a></li>
      <li><a href="dart-convert/htmlEscape-constant.html">htmlEscape</a></li>
      <li><a href="dart-convert/json-constant.html">json</a></li>
      <li><a href="dart-convert/latin1-constant.html">latin1</a></li>
      <li><a href="dart-convert/unicodeBomCharacterRune-constant.html">unicodeBomCharacterRune</a></li>
      <li><a href="dart-convert/unicodeReplacementCharacterRune-constant.html">unicodeReplacementCharacterRune</a></li>
      <li><a href="dart-convert/utf8-constant.html">utf8</a></li>
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#functions">Functions</a></li>
      <li><a href="dart-convert/base64Decode.html">base64Decode</a></li>
      <li><a href="dart-convert/base64Encode.html">base64Encode</a></li>
      <li><a href="dart-convert/base64UrlEncode.html">base64UrlEncode</a></li>
      <li><a href="dart-convert/jsonDecode.html">jsonDecode</a></li>
      <li><a href="dart-convert/jsonEncode.html">jsonEncode</a></li>
    
    
    
      <li class="section-title"><a href="dart-convert/dart-convert-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-convert/JsonCyclicError-class.html">JsonCyclicError</a></li>
      <li><a href="dart-convert/JsonUnsupportedObjectError-class.html">JsonUnsupportedObjectError</a></li>
    </ol>
  </div><!--/sidebar-offcanvas-right-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
