<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the bind method from the JsonUtf8Encoder class, for the Dart programming language.">
  <title>bind method - JsonUtf8Encoder class - dart:convert library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
    <li><a href="dart-convert/JsonUtf8Encoder-class.html">JsonUtf8Encoder</a></li>
    <li class="self-crumb">bind method</li>
  </ol>
  <div class="self-name">bind</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-convert/dart-convert-library.html">dart:convert</a></li>
      <li><a href="dart-convert/JsonUtf8Encoder-class.html">JsonUtf8Encoder</a></li>
      <li class="self-crumb">bind method</li>
    </ol>
    
    <h5>JsonUtf8Encoder class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-convert/JsonUtf8Encoder-class.html#constructors">Constructors</a></li>
        <li><a href="dart-convert/JsonUtf8Encoder/JsonUtf8Encoder.html">JsonUtf8Encoder</a></li>
    
        <li class="section-title inherited">
            <a href="dart-convert/JsonUtf8Encoder-class.html#instance-properties">Properties</a>
        </li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-convert/JsonUtf8Encoder-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-convert/JsonUtf8Encoder/bind.html">bind</a></li>
        <li><a href="dart-convert/JsonUtf8Encoder/convert.html">convert</a></li>
        <li><a href="dart-convert/JsonUtf8Encoder/startChunkedConversion.html">startChunkedConversion</a></li>
        <li class="inherited"><a href="dart-convert/Converter/cast.html">cast</a></li>
        <li class="inherited"><a href="dart-convert/Converter/fuse.html">fuse</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-convert/JsonUtf8Encoder-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
        <li class="section-title"><a href="dart-convert/JsonUtf8Encoder-class.html#constants">Constants</a></li>
        <li><a class="deprecated" href="dart-convert/JsonUtf8Encoder/DEFAULT_BUFFER_SIZE-constant.html">DEFAULT_BUFFER_SIZE</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">bind</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/int-class.html">int</a></span>&gt;</span></span>&gt;</span></span>
            <span class="name ">bind</span>
(<wbr><span class="parameter" id="bind-param-stream"><span class="type-annotation"><a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter"><a href="dart-core/Object-class.html">Object</a></span>&gt;</span></span> <span class="parameter-name">stream</span></span>)
      <div class="features">override</div>
    </section>
    <section class="desc markdown">
      <p>Transforms the provided <code>stream</code>.</p>
<p>Returns a new stream with events that are computed from events of the
provided <code>stream</code>.</p>
<p>The <a href="dart-async/StreamTransformer-class.html">StreamTransformer</a> interface is completely generic,
so it cannot say what subclasses do.
Each <a href="dart-async/StreamTransformer-class.html">StreamTransformer</a> should document clearly how it transforms the
stream (on the class or variable used to access the transformer),
as well as any differences from the following typical behavior:</p>
<ul>
<li>When the returned stream is listened to, it starts listening to the
input <code>stream</code>.</li>
<li>Subscriptions of the returned stream forward (in a reasonable time)
a <a href="dart-async/StreamSubscription/pause.html">StreamSubscription.pause</a> call to the subscription of the input
<code>stream</code>.</li>
<li>Similarly, canceling a subscription of the returned stream eventually
(in reasonable time) cancels the subscription of the input <code>stream</code>.</li>
</ul>
<p>"Reasonable time" depends on the transformer and stream. Some transformers,
like a "timeout" transformer, might make these operations depend on a
duration. Others might not delay them at all, or just by a microtask.</p>
<p>Transformers are free to handle errors in any way.
A transformer implementation may choose to propagate errors,
or convert them to other events, or ignore them completely,
but if errors are ignored, it should be documented explicitly.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Stream&lt;List&lt;int&gt;&gt; bind(Stream&lt;Object?&gt; stream) {
  return super.bind(stream);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
