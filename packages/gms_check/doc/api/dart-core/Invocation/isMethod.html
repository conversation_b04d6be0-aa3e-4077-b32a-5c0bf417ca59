<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the isMethod property from the Invocation class, for the Dart programming language.">
  <title>isMethod property - Invocation class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
    <li class="self-crumb">isMethod property</li>
  </ol>
  <div class="self-name">isMethod</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
      <li class="self-crumb">isMethod property</li>
    </ol>
    
    <h5>Invocation class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/Invocation-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/Invocation/Invocation.html">Invocation</a></li>
        <li><a href="dart-core/Invocation/Invocation.genericMethod.html">genericMethod</a></li>
        <li><a href="dart-core/Invocation/Invocation.getter.html">getter</a></li>
        <li><a href="dart-core/Invocation/Invocation.method.html">method</a></li>
        <li><a href="dart-core/Invocation/Invocation.setter.html">setter</a></li>
    
        <li class="section-title">
            <a href="dart-core/Invocation-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/Invocation/isAccessor.html">isAccessor</a></li>
        <li><a href="dart-core/Invocation/isGetter.html">isGetter</a></li>
        <li><a href="dart-core/Invocation/isMethod.html">isMethod</a></li>
        <li><a href="dart-core/Invocation/isSetter.html">isSetter</a></li>
        <li><a href="dart-core/Invocation/memberName.html">memberName</a></li>
        <li><a href="dart-core/Invocation/namedArguments.html">namedArguments</a></li>
        <li><a href="dart-core/Invocation/positionalArguments.html">positionalArguments</a></li>
        <li><a href="dart-core/Invocation/typeArguments.html">typeArguments</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title inherited"><a href="dart-core/Invocation-class.html#instance-methods">Methods</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title inherited"><a href="dart-core/Invocation-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-property">isMethod</span> property</h1></div>


        <section id="getter">
        
        <section class="multi-line-signature">
          <span class="returntype"><a href="dart-core/bool-class.html">bool</a></span>
                  <span class="name ">isMethod</span>
          
</section>
        
                <section class="desc markdown">
          <p>Whether the invocation was a method call.</p>
        </section>
                <section class="summary source-code" id="source">
          <h2><span>Implementation</span></h2>
          <pre class="language-dart"><code class="language-dart">bool get isMethod;</code></pre>
        </section>
</section>
        
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
