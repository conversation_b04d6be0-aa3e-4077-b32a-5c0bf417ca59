<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the october constant from the DateTime class, for the Dart programming language.">
  <title>october constant - DateTime class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
    <li class="self-crumb">october constant</li>
  </ol>
  <div class="self-name">october</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li class="self-crumb">october constant</li>
    </ol>
    
    <h5>DateTime class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/DateTime/DateTime.html">DateTime</a></li>
        <li><a href="dart-core/DateTime/DateTime.fromMicrosecondsSinceEpoch.html">fromMicrosecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/DateTime.fromMillisecondsSinceEpoch.html">fromMillisecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/DateTime.now.html">now</a></li>
        <li><a href="dart-core/DateTime/DateTime.utc.html">utc</a></li>
    
        <li class="section-title">
            <a href="dart-core/DateTime-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/DateTime/day.html">day</a></li>
        <li><a href="dart-core/DateTime/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/DateTime/hour.html">hour</a></li>
        <li><a href="dart-core/DateTime/isUtc.html">isUtc</a></li>
        <li><a href="dart-core/DateTime/microsecond.html">microsecond</a></li>
        <li><a href="dart-core/DateTime/microsecondsSinceEpoch.html">microsecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/millisecond.html">millisecond</a></li>
        <li><a href="dart-core/DateTime/millisecondsSinceEpoch.html">millisecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/minute.html">minute</a></li>
        <li><a href="dart-core/DateTime/month.html">month</a></li>
        <li><a href="dart-core/DateTime/second.html">second</a></li>
        <li><a href="dart-core/DateTime/timeZoneName.html">timeZoneName</a></li>
        <li><a href="dart-core/DateTime/timeZoneOffset.html">timeZoneOffset</a></li>
        <li><a href="dart-core/DateTime/weekday.html">weekday</a></li>
        <li><a href="dart-core/DateTime/year.html">year</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/DateTime/add.html">add</a></li>
        <li><a href="dart-core/DateTime/compareTo.html">compareTo</a></li>
        <li><a href="dart-core/DateTime/difference.html">difference</a></li>
        <li><a href="dart-core/DateTime/isAfter.html">isAfter</a></li>
        <li><a href="dart-core/DateTime/isAtSameMomentAs.html">isAtSameMomentAs</a></li>
        <li><a href="dart-core/DateTime/isBefore.html">isBefore</a></li>
        <li><a href="dart-core/DateTime/subtract.html">subtract</a></li>
        <li><a href="dart-core/DateTime/toIso8601String.html">toIso8601String</a></li>
        <li><a href="dart-core/DateTime/toLocal.html">toLocal</a></li>
        <li><a href="dart-core/DateTime/toString.html">toString</a></li>
        <li><a href="dart-core/DateTime/toUtc.html">toUtc</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#operators">Operators</a></li>
        <li><a href="dart-core/DateTime/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/DateTime/parse.html">parse</a></li>
        <li><a href="dart-core/DateTime/tryParse.html">tryParse</a></li>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#constants">Constants</a></li>
        <li><a href="dart-core/DateTime/april-constant.html">april</a></li>
        <li><a href="dart-core/DateTime/august-constant.html">august</a></li>
        <li><a href="dart-core/DateTime/daysPerWeek-constant.html">daysPerWeek</a></li>
        <li><a href="dart-core/DateTime/december-constant.html">december</a></li>
        <li><a href="dart-core/DateTime/february-constant.html">february</a></li>
        <li><a href="dart-core/DateTime/friday-constant.html">friday</a></li>
        <li><a href="dart-core/DateTime/january-constant.html">january</a></li>
        <li><a href="dart-core/DateTime/july-constant.html">july</a></li>
        <li><a href="dart-core/DateTime/june-constant.html">june</a></li>
        <li><a href="dart-core/DateTime/march-constant.html">march</a></li>
        <li><a href="dart-core/DateTime/may-constant.html">may</a></li>
        <li><a href="dart-core/DateTime/monday-constant.html">monday</a></li>
        <li><a href="dart-core/DateTime/monthsPerYear-constant.html">monthsPerYear</a></li>
        <li><a href="dart-core/DateTime/november-constant.html">november</a></li>
        <li><a href="dart-core/DateTime/october-constant.html">october</a></li>
        <li><a href="dart-core/DateTime/saturday-constant.html">saturday</a></li>
        <li><a href="dart-core/DateTime/september-constant.html">september</a></li>
        <li><a href="dart-core/DateTime/sunday-constant.html">sunday</a></li>
        <li><a href="dart-core/DateTime/thursday-constant.html">thursday</a></li>
        <li><a href="dart-core/DateTime/tuesday-constant.html">tuesday</a></li>
        <li><a href="dart-core/DateTime/wednesday-constant.html">wednesday</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constant">october</span> constant</h1></div>

    <section class="multi-line-signature">
        <span class="returntype"><a href="dart-core/int-class.html">int</a></span>
        const <span class="name ">october</span>
        =
        <span class="constant-value">10</span>
    </section>

        <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static const int october = 10

</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
