<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the parse method from the DateTime class, for the Dart programming language.">
  <title>parse method - DateTime class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
    <li class="self-crumb">parse method</li>
  </ol>
  <div class="self-name">parse</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li class="self-crumb">parse method</li>
    </ol>
    
    <h5>DateTime class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/DateTime/DateTime.html">DateTime</a></li>
        <li><a href="dart-core/DateTime/DateTime.fromMicrosecondsSinceEpoch.html">fromMicrosecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/DateTime.fromMillisecondsSinceEpoch.html">fromMillisecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/DateTime.now.html">now</a></li>
        <li><a href="dart-core/DateTime/DateTime.utc.html">utc</a></li>
    
        <li class="section-title">
            <a href="dart-core/DateTime-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/DateTime/day.html">day</a></li>
        <li><a href="dart-core/DateTime/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/DateTime/hour.html">hour</a></li>
        <li><a href="dart-core/DateTime/isUtc.html">isUtc</a></li>
        <li><a href="dart-core/DateTime/microsecond.html">microsecond</a></li>
        <li><a href="dart-core/DateTime/microsecondsSinceEpoch.html">microsecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/millisecond.html">millisecond</a></li>
        <li><a href="dart-core/DateTime/millisecondsSinceEpoch.html">millisecondsSinceEpoch</a></li>
        <li><a href="dart-core/DateTime/minute.html">minute</a></li>
        <li><a href="dart-core/DateTime/month.html">month</a></li>
        <li><a href="dart-core/DateTime/second.html">second</a></li>
        <li><a href="dart-core/DateTime/timeZoneName.html">timeZoneName</a></li>
        <li><a href="dart-core/DateTime/timeZoneOffset.html">timeZoneOffset</a></li>
        <li><a href="dart-core/DateTime/weekday.html">weekday</a></li>
        <li><a href="dart-core/DateTime/year.html">year</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/DateTime/add.html">add</a></li>
        <li><a href="dart-core/DateTime/compareTo.html">compareTo</a></li>
        <li><a href="dart-core/DateTime/difference.html">difference</a></li>
        <li><a href="dart-core/DateTime/isAfter.html">isAfter</a></li>
        <li><a href="dart-core/DateTime/isAtSameMomentAs.html">isAtSameMomentAs</a></li>
        <li><a href="dart-core/DateTime/isBefore.html">isBefore</a></li>
        <li><a href="dart-core/DateTime/subtract.html">subtract</a></li>
        <li><a href="dart-core/DateTime/toIso8601String.html">toIso8601String</a></li>
        <li><a href="dart-core/DateTime/toLocal.html">toLocal</a></li>
        <li><a href="dart-core/DateTime/toString.html">toString</a></li>
        <li><a href="dart-core/DateTime/toUtc.html">toUtc</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#operators">Operators</a></li>
        <li><a href="dart-core/DateTime/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/DateTime/parse.html">parse</a></li>
        <li><a href="dart-core/DateTime/tryParse.html">tryParse</a></li>
    
        <li class="section-title"><a href="dart-core/DateTime-class.html#constants">Constants</a></li>
        <li><a href="dart-core/DateTime/april-constant.html">april</a></li>
        <li><a href="dart-core/DateTime/august-constant.html">august</a></li>
        <li><a href="dart-core/DateTime/daysPerWeek-constant.html">daysPerWeek</a></li>
        <li><a href="dart-core/DateTime/december-constant.html">december</a></li>
        <li><a href="dart-core/DateTime/february-constant.html">february</a></li>
        <li><a href="dart-core/DateTime/friday-constant.html">friday</a></li>
        <li><a href="dart-core/DateTime/january-constant.html">january</a></li>
        <li><a href="dart-core/DateTime/july-constant.html">july</a></li>
        <li><a href="dart-core/DateTime/june-constant.html">june</a></li>
        <li><a href="dart-core/DateTime/march-constant.html">march</a></li>
        <li><a href="dart-core/DateTime/may-constant.html">may</a></li>
        <li><a href="dart-core/DateTime/monday-constant.html">monday</a></li>
        <li><a href="dart-core/DateTime/monthsPerYear-constant.html">monthsPerYear</a></li>
        <li><a href="dart-core/DateTime/november-constant.html">november</a></li>
        <li><a href="dart-core/DateTime/october-constant.html">october</a></li>
        <li><a href="dart-core/DateTime/saturday-constant.html">saturday</a></li>
        <li><a href="dart-core/DateTime/september-constant.html">september</a></li>
        <li><a href="dart-core/DateTime/sunday-constant.html">sunday</a></li>
        <li><a href="dart-core/DateTime/thursday-constant.html">thursday</a></li>
        <li><a href="dart-core/DateTime/tuesday-constant.html">tuesday</a></li>
        <li><a href="dart-core/DateTime/wednesday-constant.html">wednesday</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">parse</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/DateTime-class.html">DateTime</a></span>
            <span class="name ">parse</span>
(<wbr><span class="parameter" id="parse-param-formattedString"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">formattedString</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Constructs a new <a href="dart-core/DateTime-class.html">DateTime</a> instance based on <code>formattedString</code>.</p>
<p>Throws a <a href="dart-core/FormatException-class.html">FormatException</a> if the input string cannot be parsed.</p>
<p>The function parses a subset of ISO 8601
which includes the subset accepted by RFC 3339.</p>
<p>The accepted inputs are currently:</p>
<ul>
<li>A date: A signed four-to-six digit year, two digit month and
two digit day, optionally separated by <code>-</code> characters.
Examples: "19700101", "-0004-12-24", "81030-04-01".</li>
<li>An optional time part, separated from the date by either <code>T</code> or a space.
The time part is a two digit hour,
then optionally a two digit minutes value,
then optionally a two digit seconds value, and
then optionally a '.' or ',' followed by at least a one digit
second fraction.
The minutes and seconds may be separated from the previous parts by a
':'.
Examples: "12", "12:30:24.124", "12:30:24,124", "123010.50".</li>
<li>An optional time-zone offset part,
possibly separated from the previous by a space.
The time zone is either 'z' or 'Z', or it is a signed two digit hour
part and an optional two digit minute part. The sign must be either
"+" or "-", and can not be omitted.
The minutes may be separated from the hours by a ':'.
Examples: "Z", "-10", "+01:30", "+1130".</li>
</ul>
<p>This includes the output of both <a href="dart-core/DateTime/toString.html">toString</a> and <a href="dart-core/DateTime/toIso8601String.html">toIso8601String</a>, which
will be parsed back into a <code>DateTime</code> object with the same time as the
original.</p>
<p>The result is always in either local time or UTC.
If a time zone offset other than UTC is specified,
the time is converted to the equivalent UTC time.</p>
<p>Examples of accepted strings:</p>
<ul>
<li><code>"2012-02-27"</code></li>
<li><code>"2012-02-27 13:27:00"</code></li>
<li><code>"2012-02-27 13:27:00.123456789z"</code></li>
<li><code>"2012-02-27 13:27:00,123456789z"</code></li>
<li><code>"20120227 13:27:00"</code></li>
<li><code>"20120227T132700"</code></li>
<li><code>"20120227"</code></li>
<li><code>"+20120227"</code></li>
<li><code>"2012-02-27T14Z"</code></li>
<li><code>"2012-02-27T14+00:00"</code></li>
<li><code>"-123450101 00:00:00 Z"</code>: in the year -12345.</li>
<li><code>"2002-02-27T14:00:00-0500"</code>: Same as <code>"2002-02-27T19:00:00Z"</code></li>
</ul>
<p>This method accepts out-of-range component values and interprets
them as overflows into the next larger component.
For example, "2020-01-42" will be parsed as 2020-02-11, because
the last valid date in that month is 2020-01-31, so 42 days is
interprted as 31 days of that month plus 11 days into the next month.</p>
<p>To detect and reject invalid component values, use
<a href="https://pub.dev/documentation/intl/latest/intl/DateFormat/parseStrict.html">DateFormat.parseStrict</a>
from the <a href="https://pub.dev/packages/intl">intl</a> package.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static DateTime parse(String formattedString) {
  var re = _parseFormat;
  Match? match = re.firstMatch(formattedString);
  if (match != null) {
    int parseIntOrZero(String? matched) {
      if (matched == null) return 0;
      return int.parse(matched);
    }

    &#47;&#47; Parses fractional second digits of &#39;.(\d+)&#39; into the combined
    &#47;&#47; microseconds. We only use the first 6 digits because of DateTime
    &#47;&#47; precision of 999 milliseconds and 999 microseconds.
    int parseMilliAndMicroseconds(String? matched) {
      if (matched == null) return 0;
      int length = matched.length;
      assert(length &gt;= 1);
      int result = 0;
      for (int i = 0; i &lt; 6; i++) {
        result *= 10;
        if (i &lt; matched.length) {
          result += matched.codeUnitAt(i) ^ 0x30;
        }
      }
      return result;
    }

    int years = int.parse(match[1]!);
    int month = int.parse(match[2]!);
    int day = int.parse(match[3]!);
    int hour = parseIntOrZero(match[4]);
    int minute = parseIntOrZero(match[5]);
    int second = parseIntOrZero(match[6]);
    int milliAndMicroseconds = parseMilliAndMicroseconds(match[7]);
    int millisecond =
        milliAndMicroseconds ~&#47; Duration.microsecondsPerMillisecond;
    int microsecond = milliAndMicroseconds
        .remainder(Duration.microsecondsPerMillisecond) as int;
    bool isUtc = false;
    if (match[8] != null) {
      &#47;&#47; timezone part
      isUtc = true;
      String? tzSign = match[9];
      if (tzSign != null) {
        &#47;&#47; timezone other than &#39;Z&#39; and &#39;z&#39;.
        int sign = (tzSign == &#39;-&#39;) ? -1 : 1;
        int hourDifference = int.parse(match[10]!);
        int minuteDifference = parseIntOrZero(match[11]);
        minuteDifference += 60 * hourDifference;
        minute -= sign * minuteDifference;
      }
    }
    int? value = _brokenDownDateToValue(years, month, day, hour, minute,
        second, millisecond, microsecond, isUtc);
    if (value == null) {
      throw FormatException(&quot;Time out of range&quot;, formattedString);
    }
    return DateTime._withValue(value, isUtc: isUtc);
  } else {
    throw FormatException(&quot;Invalid date format&quot;, formattedString);
  }
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
