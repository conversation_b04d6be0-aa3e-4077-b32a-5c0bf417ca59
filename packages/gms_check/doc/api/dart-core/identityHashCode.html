<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the identityHashCode function from the dart:core library, for the Dart programming language.">
  <title>identityHashCode function - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li class="self-crumb">identityHashCode function</li>
  </ol>
  <div class="self-name">identityHashCode</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li class="self-crumb">identityHashCode function</li>
    </ol>
    
    <h5>dart:core library</h5>
    <ol>
      <li class="section-title"><a href="dart-core/dart-core-library.html#classes">Classes</a></li>
      <li><a href="dart-core/BidirectionalIterator-class.html">BidirectionalIterator</a></li>
      <li><a href="dart-core/BigInt-class.html">BigInt</a></li>
      <li><a href="dart-core/bool-class.html">bool</a></li>
      <li><a href="dart-core/Comparable-class.html">Comparable</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li><a href="dart-core/Deprecated-class.html">Deprecated</a></li>
      <li><a href="dart-core/double-class.html">double</a></li>
      <li><a href="dart-core/Duration-class.html">Duration</a></li>
      <li><a href="dart-core/Enum-class.html">Enum</a></li>
      <li><a href="dart-core/Expando-class.html">Expando</a></li>
      <li><a href="dart-core/Function-class.html">Function</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-core/int-class.html">int</a></li>
      <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
      <li><a href="dart-core/Iterable-class.html">Iterable</a></li>
      <li><a href="dart-core/Iterator-class.html">Iterator</a></li>
      <li><a href="dart-core/List-class.html">List</a></li>
      <li><a href="dart-core/Map-class.html">Map</a></li>
      <li><a href="dart-core/MapEntry-class.html">MapEntry</a></li>
      <li><a href="dart-core/Match-class.html">Match</a></li>
      <li><a href="dart-core/Null-class.html">Null</a></li>
      <li><a href="dart-core/num-class.html">num</a></li>
      <li><a href="dart-core/Object-class.html">Object</a></li>
      <li><a href="dart-core/Pattern-class.html">Pattern</a></li>
      <li><a href="dart-core/pragma-class.html">pragma</a></li>
      <li><a class="deprecated" href="dart-core/Provisional-class.html">Provisional</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li><a href="dart-core/RegExpMatch-class.html">RegExpMatch</a></li>
      <li><a href="dart-core/RuneIterator-class.html">RuneIterator</a></li>
      <li><a href="dart-core/Runes-class.html">Runes</a></li>
      <li><a href="dart-core/Set-class.html">Set</a></li>
      <li><a href="dart-core/Sink-class.html">Sink</a></li>
      <li><a href="dart-core/StackTrace-class.html">StackTrace</a></li>
      <li><a href="dart-core/Stopwatch-class.html">Stopwatch</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li><a href="dart-core/StringBuffer-class.html">StringBuffer</a></li>
      <li><a href="dart-core/StringSink-class.html">StringSink</a></li>
      <li><a href="dart-core/Symbol-class.html">Symbol</a></li>
      <li><a href="dart-core/Type-class.html">Type</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li><a href="dart-core/UriData-class.html">UriData</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#constants">Constants</a></li>
      <li><a href="dart-core/deprecated-constant.html">deprecated</a></li>
      <li><a href="dart-core/override-constant.html">override</a></li>
      <li><a class="deprecated" href="dart-core/provisional-constant.html">provisional</a></li>
      <li><a class="deprecated" href="dart-core/proxy-constant.html">proxy</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#functions">Functions</a></li>
      <li><a href="dart-core/identical.html">identical</a></li>
      <li><a href="dart-core/identityHashCode.html">identityHashCode</a></li>
      <li><a href="dart-core/print.html">print</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-core/Comparator.html">Comparator</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-core/AbstractClassInstantiationError-class.html">AbstractClassInstantiationError</a></li>
      <li><a href="dart-core/ArgumentError-class.html">ArgumentError</a></li>
      <li><a href="dart-core/AssertionError-class.html">AssertionError</a></li>
      <li><a class="deprecated" href="dart-core/CastError-class.html">CastError</a></li>
      <li><a href="dart-core/ConcurrentModificationError-class.html">ConcurrentModificationError</a></li>
      <li><a href="dart-core/CyclicInitializationError-class.html">CyclicInitializationError</a></li>
      <li><a href="dart-core/Error-class.html">Error</a></li>
      <li><a href="dart-core/Exception-class.html">Exception</a></li>
      <li><a href="dart-core/FallThroughError-class.html">FallThroughError</a></li>
      <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
      <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
      <li><a href="dart-core/IntegerDivisionByZeroException-class.html">IntegerDivisionByZeroException</a></li>
      <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
      <li><a href="dart-core/NullThrownError-class.html">NullThrownError</a></li>
      <li><a href="dart-core/OutOfMemoryError-class.html">OutOfMemoryError</a></li>
      <li><a href="dart-core/RangeError-class.html">RangeError</a></li>
      <li><a href="dart-core/StackOverflowError-class.html">StackOverflowError</a></li>
      <li><a href="dart-core/StateError-class.html">StateError</a></li>
      <li><a href="dart-core/TypeError-class.html">TypeError</a></li>
      <li><a href="dart-core/UnimplementedError-class.html">UnimplementedError</a></li>
      <li><a href="dart-core/UnsupportedError-class.html">UnsupportedError</a></li>
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-function">identityHashCode</span> function </h1></div>

    <section class="multi-line-signature">
        <span class="returntype"><a href="dart-core/int-class.html">int</a></span>
                <span class="name ">identityHashCode</span>
(<wbr><span class="parameter" id="identityHashCode-param-object"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">object</span></span>)
    </section>
    <section class="desc markdown">
      <p>The identity hash code of <code>object</code>.</p>
<p>Returns the value that the original <a href="dart-core/Object/hashCode.html">Object.hashCode</a> would return
on this object, even if <code>hashCode</code> has been overridden.</p>
<p>This hash code is compatible with <a href="dart-core/identical.html">identical</a>,
which just means that it's guaranteed to be stable over time.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">@pragma(&quot;vm:entry-point&quot;)
external int identityHashCode(Object? object);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
