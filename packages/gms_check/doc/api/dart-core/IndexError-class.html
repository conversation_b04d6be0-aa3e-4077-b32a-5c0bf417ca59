<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the IndexError class from the dart:core library, for the Dart programming language.">
  <title>IndexError class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li class="self-crumb">IndexError class</li>
  </ol>
  <div class="self-name">IndexError</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li class="self-crumb">IndexError class</li>
    </ol>
    
    <h5>dart:core library</h5>
    <ol>
      <li class="section-title"><a href="dart-core/dart-core-library.html#classes">Classes</a></li>
      <li><a href="dart-core/BidirectionalIterator-class.html">BidirectionalIterator</a></li>
      <li><a href="dart-core/BigInt-class.html">BigInt</a></li>
      <li><a href="dart-core/bool-class.html">bool</a></li>
      <li><a href="dart-core/Comparable-class.html">Comparable</a></li>
      <li><a href="dart-core/DateTime-class.html">DateTime</a></li>
      <li><a href="dart-core/Deprecated-class.html">Deprecated</a></li>
      <li><a href="dart-core/double-class.html">double</a></li>
      <li><a href="dart-core/Duration-class.html">Duration</a></li>
      <li><a href="dart-core/Enum-class.html">Enum</a></li>
      <li><a href="dart-core/Expando-class.html">Expando</a></li>
      <li><a href="dart-core/Function-class.html">Function</a></li>
      <li><a href="dart-async/Future-class.html">Future</a></li>
      <li><a href="dart-core/int-class.html">int</a></li>
      <li><a href="dart-core/Invocation-class.html">Invocation</a></li>
      <li><a href="dart-core/Iterable-class.html">Iterable</a></li>
      <li><a href="dart-core/Iterator-class.html">Iterator</a></li>
      <li><a href="dart-core/List-class.html">List</a></li>
      <li><a href="dart-core/Map-class.html">Map</a></li>
      <li><a href="dart-core/MapEntry-class.html">MapEntry</a></li>
      <li><a href="dart-core/Match-class.html">Match</a></li>
      <li><a href="dart-core/Null-class.html">Null</a></li>
      <li><a href="dart-core/num-class.html">num</a></li>
      <li><a href="dart-core/Object-class.html">Object</a></li>
      <li><a href="dart-core/Pattern-class.html">Pattern</a></li>
      <li><a href="dart-core/pragma-class.html">pragma</a></li>
      <li><a class="deprecated" href="dart-core/Provisional-class.html">Provisional</a></li>
      <li><a href="dart-core/RegExp-class.html">RegExp</a></li>
      <li><a href="dart-core/RegExpMatch-class.html">RegExpMatch</a></li>
      <li><a href="dart-core/RuneIterator-class.html">RuneIterator</a></li>
      <li><a href="dart-core/Runes-class.html">Runes</a></li>
      <li><a href="dart-core/Set-class.html">Set</a></li>
      <li><a href="dart-core/Sink-class.html">Sink</a></li>
      <li><a href="dart-core/StackTrace-class.html">StackTrace</a></li>
      <li><a href="dart-core/Stopwatch-class.html">Stopwatch</a></li>
      <li><a href="dart-async/Stream-class.html">Stream</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li><a href="dart-core/StringBuffer-class.html">StringBuffer</a></li>
      <li><a href="dart-core/StringSink-class.html">StringSink</a></li>
      <li><a href="dart-core/Symbol-class.html">Symbol</a></li>
      <li><a href="dart-core/Type-class.html">Type</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li><a href="dart-core/UriData-class.html">UriData</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#extension">Extensions</a></li>
      <li><a href="dart-async/FutureExtensions.html">FutureExtensions</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#constants">Constants</a></li>
      <li><a href="dart-core/deprecated-constant.html">deprecated</a></li>
      <li><a href="dart-core/override-constant.html">override</a></li>
      <li><a class="deprecated" href="dart-core/provisional-constant.html">provisional</a></li>
      <li><a class="deprecated" href="dart-core/proxy-constant.html">proxy</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#functions">Functions</a></li>
      <li><a href="dart-core/identical.html">identical</a></li>
      <li><a href="dart-core/identityHashCode.html">identityHashCode</a></li>
      <li><a href="dart-core/print.html">print</a></li>
    
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#typedefs">Typedefs</a></li>
      <li><a href="dart-core/Comparator.html">Comparator</a></li>
    
      <li class="section-title"><a href="dart-core/dart-core-library.html#exceptions">Exceptions</a></li>
      <li><a href="dart-core/AbstractClassInstantiationError-class.html">AbstractClassInstantiationError</a></li>
      <li><a href="dart-core/ArgumentError-class.html">ArgumentError</a></li>
      <li><a href="dart-core/AssertionError-class.html">AssertionError</a></li>
      <li><a class="deprecated" href="dart-core/CastError-class.html">CastError</a></li>
      <li><a href="dart-core/ConcurrentModificationError-class.html">ConcurrentModificationError</a></li>
      <li><a href="dart-core/CyclicInitializationError-class.html">CyclicInitializationError</a></li>
      <li><a href="dart-core/Error-class.html">Error</a></li>
      <li><a href="dart-core/Exception-class.html">Exception</a></li>
      <li><a href="dart-core/FallThroughError-class.html">FallThroughError</a></li>
      <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
      <li><a href="dart-core/IndexError-class.html">IndexError</a></li>
      <li><a href="dart-core/IntegerDivisionByZeroException-class.html">IntegerDivisionByZeroException</a></li>
      <li><a href="dart-core/NoSuchMethodError-class.html">NoSuchMethodError</a></li>
      <li><a href="dart-core/NullThrownError-class.html">NullThrownError</a></li>
      <li><a href="dart-core/OutOfMemoryError-class.html">OutOfMemoryError</a></li>
      <li><a href="dart-core/RangeError-class.html">RangeError</a></li>
      <li><a href="dart-core/StackOverflowError-class.html">StackOverflowError</a></li>
      <li><a href="dart-core/StateError-class.html">StateError</a></li>
      <li><a href="dart-core/TypeError-class.html">TypeError</a></li>
      <li><a href="dart-core/UnimplementedError-class.html">UnimplementedError</a></li>
      <li><a href="dart-core/UnsupportedError-class.html">UnsupportedError</a></li>
    </ol>
  </div>

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-class">IndexError</span> class </h1></div>

    <section class="desc markdown">
      <p>A specialized <a href="dart-core/RangeError-class.html">RangeError</a> used when an index is not in the range
<code>0..indexable.length-1</code>.</p>
<p>Also contains the indexable object, its length at the time of the error,
and the invalid index itself.</p>
    </section>
    
    <section>
      <dl class="dl-horizontal">
        <dt>Inheritance</dt>
        <dd><ul class="gt-separated dark clazz-relationships">
          <li><a href="dart-core/Object-class.html">Object</a></li>
          <li><a href="dart-core/Error-class.html">Error</a></li>
          <li><a href="dart-core/ArgumentError-class.html">ArgumentError</a></li>
          <li>IndexError</li>
        </ul></dd>

        <dt>Implemented types</dt>
        <dd>
          <ul class="comma-separated clazz-relationships">
            <li><a href="dart-core/RangeError-class.html">RangeError</a></li>
          </ul>
        </dd>




      </dl>
    </section>

    <section class="summary offset-anchor" id="constructors">
      <h2>Constructors</h2>

      <dl class="constructor-summary-list">
        <dt id="IndexError" class="callable">
          <span class="name"><a href="dart-core/IndexError/IndexError.html">IndexError</a></span><span class="signature">(<span class="parameter" id="-param-invalidValue"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">invalidValue</span>, </span> <span class="parameter" id="-param-indexable"><span class="type-annotation">dynamic</span> <span class="parameter-name">indexable</span>, [</span> <span class="parameter" id="-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, </span> <span class="parameter" id="-param-message"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">message</span>, </span> <span class="parameter" id="-param-length"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">length</span></span> ])</span>
        </dt>
        <dd>
          Creates a new <a href="dart-core/IndexError-class.html">IndexError</a> stating that <code>invalidValue</code> is not a valid index
into <code>indexable</code>. <a href="dart-core/IndexError/IndexError.html">[...]</a>
        </dd>
      </dl>
    </section>

    <section class="summary offset-anchor" id="instance-properties">
      <h2>Properties</h2>

      <dl class="properties">
        <dt id="end" class="property">
          <span class="name"><a href="dart-core/IndexError/end.html">end</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          The maximum value that <a href="dart-core/RangeError/RangeError.value.html">value</a> is allowed to assume.
                  <div class="features">read-only, override</div>
</dd>
        <dt id="indexable" class="property">
          <span class="name"><a href="dart-core/IndexError/indexable.html">indexable</a></span>
          <span class="signature">&#8594; dynamic</span>         
        </dt>
        <dd>
          The indexable object that <a href="dart-core/ArgumentError/invalidValue.html">invalidValue</a> was not a valid index into.
                  <div class="features">final</div>
</dd>
        <dt id="length" class="property">
          <span class="name"><a href="dart-core/IndexError/length.html">length</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          The length of <a href="dart-core/IndexError/indexable.html">indexable</a> at the time of the error.
                  <div class="features">final</div>
</dd>
        <dt id="start" class="property">
          <span class="name"><a href="dart-core/IndexError/start.html">start</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd>
          The minimum value that <a href="dart-core/RangeError/RangeError.value.html">value</a> is allowed to assume.
                  <div class="features">read-only, override</div>
</dd>
        <dt id="hashCode" class="property inherited">
          <span class="name"><a href="dart-core/Object/hashCode.html">hashCode</a></span>
          <span class="signature">&#8594; <a href="dart-core/int-class.html">int</a></span>         
        </dt>
        <dd class="inherited">
          The hash code for this object. <a href="dart-core/Object/hashCode.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="invalidValue" class="property inherited">
          <span class="name"><a href="dart-core/ArgumentError/invalidValue.html">invalidValue</a></span>
          <span class="signature">&#8594; dynamic</span>         
        </dt>
        <dd class="inherited">
          The invalid value.
                  <div class="features">final, inherited</div>
</dd>
        <dt id="message" class="property inherited">
          <span class="name"><a href="dart-core/ArgumentError/message.html">message</a></span>
          <span class="signature">&#8594; dynamic</span>         
        </dt>
        <dd class="inherited">
          Message describing the problem.
                  <div class="features">final, inherited</div>
</dd>
        <dt id="name" class="property inherited">
          <span class="name"><a href="dart-core/ArgumentError/name.html">name</a></span>
          <span class="signature">&#8594; <a href="dart-core/String-class.html">String</a></span>         
        </dt>
        <dd class="inherited">
          Name of the invalid argument, if available.
                  <div class="features">final, inherited</div>
</dd>
        <dt id="runtimeType" class="property inherited">
          <span class="name"><a href="dart-core/Object/runtimeType.html">runtimeType</a></span>
          <span class="signature">&#8594; <a href="dart-core/Type-class.html">Type</a></span>         
        </dt>
        <dd class="inherited">
          A representation of the runtime type of the object.
                  <div class="features">read-only, inherited</div>
</dd>
        <dt id="stackTrace" class="property inherited">
          <span class="name"><a href="dart-core/Error/stackTrace.html">stackTrace</a></span>
          <span class="signature">&#8594; <a href="dart-core/StackTrace-class.html">StackTrace</a></span>         
        </dt>
        <dd class="inherited">
          The stack trace at the point where this error was first thrown. <a href="dart-core/Error/stackTrace.html">[...]</a>
                  <div class="features">read-only, inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="instance-methods">
      <h2>Methods</h2>
      <dl class="callables">
        <dt id="noSuchMethod" class="callable inherited">
          <span class="name"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></span><span class="signature">(<wbr><span class="parameter" id="noSuchMethod-param-invocation"><span class="type-annotation"><a href="dart-core/Invocation-class.html">Invocation</a></span> <span class="parameter-name">invocation</span></span>)
            <span class="returntype parameter">&#8594; dynamic</span>
          </span>
                  </dt>
        <dd class="inherited">
          Invoked when a non-existent method or property is accessed. <a href="dart-core/Object/noSuchMethod.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
        <dt id="toString" class="callable inherited">
          <span class="name"><a href="dart-core/ArgumentError/toString.html">toString</a></span><span class="signature">(<wbr>)
            <span class="returntype parameter">&#8594; <a href="dart-core/String-class.html">String</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          A string representation of this object. <a href="dart-core/ArgumentError/toString.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>

    <section class="summary offset-anchor inherited" id="operators">
      <h2>Operators</h2>
      <dl class="callables">
        <dt id="operator ==" class="callable inherited">
          <span class="name"><a href="dart-core/Object/operator_equals.html">operator ==</a></span><span class="signature">(<wbr><span class="parameter" id="==-param-other"><span class="type-annotation"><a href="dart-core/Object-class.html">Object</a></span> <span class="parameter-name">other</span></span>)
            <span class="returntype parameter">&#8594; <a href="dart-core/bool-class.html">bool</a></span>
          </span>
                  </dt>
        <dd class="inherited">
          The equality operator. <a href="dart-core/Object/operator_equals.html">[...]</a>
                  <div class="features">inherited</div>
</dd>
      </dl>
    </section>




  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
    <ol>
    
      <li class="section-title"><a href="dart-core/IndexError-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/IndexError/IndexError.html">IndexError</a></li>
    
      <li class="section-title">
        <a href="dart-core/IndexError-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/IndexError/end.html">end</a></li>
      <li><a href="dart-core/IndexError/indexable.html">indexable</a></li>
      <li><a href="dart-core/IndexError/length.html">length</a></li>
      <li><a href="dart-core/IndexError/start.html">start</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/invalidValue.html">invalidValue</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/message.html">message</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/name.html">name</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-core/Error/stackTrace.html">stackTrace</a></li>
    
      <li class="section-title inherited"><a href="dart-core/IndexError-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/ArgumentError/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-core/IndexError-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
