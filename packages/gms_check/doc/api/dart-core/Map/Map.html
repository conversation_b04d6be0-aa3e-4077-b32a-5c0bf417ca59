<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the map method from the Map class, for the Dart programming language.">
  <title>map method - Map class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Map-class.html">Map<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
    <li class="self-crumb">map&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt; abstract method</li>
  </ol>
  <div class="self-name">map</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Map-class.html">Map<span class="signature">&lt;<wbr><span class="type-parameter">K</span>, <span class="type-parameter">V</span>&gt;</span></a></li>
      <li class="self-crumb">map&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt; abstract method</li>
    </ol>
    
    <h5>Map class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/Map-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/Map/Map.html">Map</a></li>
        <li><a href="dart-core/Map/Map.from.html">from</a></li>
        <li><a href="dart-core/Map/Map.fromEntries.html">fromEntries</a></li>
        <li><a href="dart-core/Map/Map.fromIterable.html">fromIterable</a></li>
        <li><a href="dart-core/Map/Map.fromIterables.html">fromIterables</a></li>
        <li><a href="dart-core/Map/Map.identity.html">identity</a></li>
        <li><a href="dart-core/Map/Map.of.html">of</a></li>
        <li><a href="dart-core/Map/Map.unmodifiable.html">unmodifiable</a></li>
    
        <li class="section-title">
            <a href="dart-core/Map-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/Map/entries.html">entries</a></li>
        <li><a href="dart-core/Map/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-core/Map/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-core/Map/keys.html">keys</a></li>
        <li><a href="dart-core/Map/length.html">length</a></li>
        <li><a href="dart-core/Map/values.html">values</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/Map-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/Map/addAll.html">addAll</a></li>
        <li><a href="dart-core/Map/addEntries.html">addEntries</a></li>
        <li><a href="dart-core/Map/cast.html">cast</a></li>
        <li><a href="dart-core/Map/clear.html">clear</a></li>
        <li><a href="dart-core/Map/containsKey.html">containsKey</a></li>
        <li><a href="dart-core/Map/containsValue.html">containsValue</a></li>
        <li><a href="dart-core/Map/forEach.html">forEach</a></li>
        <li><a href="dart-core/Map/map.html">map</a></li>
        <li><a href="dart-core/Map/putIfAbsent.html">putIfAbsent</a></li>
        <li><a href="dart-core/Map/remove.html">remove</a></li>
        <li><a href="dart-core/Map/removeWhere.html">removeWhere</a></li>
        <li><a href="dart-core/Map/update.html">update</a></li>
        <li><a href="dart-core/Map/updateAll.html">updateAll</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title"><a href="dart-core/Map-class.html#operators">Operators</a></li>
        <li><a href="dart-core/Map/operator_get.html">operator []</a></li>
        <li><a href="dart-core/Map/operator_put.html">operator []=</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
        <li class="section-title"><a href="dart-core/Map-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/Map/castFrom.html">castFrom</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">map&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/Map-class.html">Map</a><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span></span>
            <span class="name ">map</span>
&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;(<wbr><span class="parameter" id="map-param-convert"><span class="type-annotation"><a href="dart-core/MapEntry-class.html">MapEntry</a><span class="signature">&lt;<wbr><span class="type-parameter">K2</span>, <span class="type-parameter">V2</span>&gt;</span></span> <span class="parameter-name">convert</span>(<span class="parameter" id="param-key"><span class="type-annotation">K</span> <span class="parameter-name">key</span>, </span> <span class="parameter" id="param-value"><span class="type-annotation">V</span> <span class="parameter-name">value</span></span>)</span>)
      
    </section>
    <section class="desc markdown">
      <p>Returns a new map where all entries of this map are transformed by
the given <code>convert</code> function.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Map&lt;K2, V2&gt; map&lt;K2, V2&gt;(MapEntry&lt;K2, V2&gt; convert(K key, V value));</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
