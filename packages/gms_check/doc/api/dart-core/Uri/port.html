<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the port property from the Uri class, for the Dart programming language.">
  <title>port property - Uri class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/Uri-class.html">Uri</a></li>
    <li class="self-crumb">port property</li>
  </ol>
  <div class="self-name">port</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/Uri-class.html">Uri</a></li>
      <li class="self-crumb">port property</li>
    </ol>
    
    <h5>Uri class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/Uri/Uri.html">Uri</a></li>
        <li><a href="dart-core/Uri/Uri.dataFromBytes.html">dataFromBytes</a></li>
        <li><a href="dart-core/Uri/Uri.dataFromString.html">dataFromString</a></li>
        <li><a href="dart-core/Uri/Uri.directory.html">directory</a></li>
        <li><a href="dart-core/Uri/Uri.file.html">file</a></li>
        <li><a href="dart-core/Uri/Uri.http.html">http</a></li>
        <li><a href="dart-core/Uri/Uri.https.html">https</a></li>
    
        <li class="section-title">
            <a href="dart-core/Uri-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/Uri/authority.html">authority</a></li>
        <li><a href="dart-core/Uri/data.html">data</a></li>
        <li><a href="dart-core/Uri/fragment.html">fragment</a></li>
        <li><a href="dart-core/Uri/hasAbsolutePath.html">hasAbsolutePath</a></li>
        <li><a href="dart-core/Uri/hasAuthority.html">hasAuthority</a></li>
        <li><a href="dart-core/Uri/hasEmptyPath.html">hasEmptyPath</a></li>
        <li><a href="dart-core/Uri/hasFragment.html">hasFragment</a></li>
        <li><a href="dart-core/Uri/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/Uri/hasPort.html">hasPort</a></li>
        <li><a href="dart-core/Uri/hasQuery.html">hasQuery</a></li>
        <li><a href="dart-core/Uri/hasScheme.html">hasScheme</a></li>
        <li><a href="dart-core/Uri/host.html">host</a></li>
        <li><a href="dart-core/Uri/isAbsolute.html">isAbsolute</a></li>
        <li><a href="dart-core/Uri/origin.html">origin</a></li>
        <li><a href="dart-core/Uri/path.html">path</a></li>
        <li><a href="dart-core/Uri/pathSegments.html">pathSegments</a></li>
        <li><a href="dart-core/Uri/port.html">port</a></li>
        <li><a href="dart-core/Uri/query.html">query</a></li>
        <li><a href="dart-core/Uri/queryParameters.html">queryParameters</a></li>
        <li><a href="dart-core/Uri/queryParametersAll.html">queryParametersAll</a></li>
        <li><a href="dart-core/Uri/scheme.html">scheme</a></li>
        <li><a href="dart-core/Uri/userInfo.html">userInfo</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/Uri/isScheme.html">isScheme</a></li>
        <li><a href="dart-core/Uri/normalizePath.html">normalizePath</a></li>
        <li><a href="dart-core/Uri/removeFragment.html">removeFragment</a></li>
        <li><a href="dart-core/Uri/replace.html">replace</a></li>
        <li><a href="dart-core/Uri/resolve.html">resolve</a></li>
        <li><a href="dart-core/Uri/resolveUri.html">resolveUri</a></li>
        <li><a href="dart-core/Uri/toFilePath.html">toFilePath</a></li>
        <li><a href="dart-core/Uri/toString.html">toString</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#operators">Operators</a></li>
        <li><a href="dart-core/Uri/operator_equals.html">operator ==</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#static-properties">Static properties</a></li>
        <li><a href="dart-core/Uri/base.html">base</a></li>
    
        <li class="section-title"><a href="dart-core/Uri-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/Uri/decodeComponent.html">decodeComponent</a></li>
        <li><a href="dart-core/Uri/decodeFull.html">decodeFull</a></li>
        <li><a href="dart-core/Uri/decodeQueryComponent.html">decodeQueryComponent</a></li>
        <li><a href="dart-core/Uri/encodeComponent.html">encodeComponent</a></li>
        <li><a href="dart-core/Uri/encodeFull.html">encodeFull</a></li>
        <li><a href="dart-core/Uri/encodeQueryComponent.html">encodeQueryComponent</a></li>
        <li><a href="dart-core/Uri/parse.html">parse</a></li>
        <li><a href="dart-core/Uri/parseIPv4Address.html">parseIPv4Address</a></li>
        <li><a href="dart-core/Uri/parseIPv6Address.html">parseIPv6Address</a></li>
        <li><a href="dart-core/Uri/splitQueryString.html">splitQueryString</a></li>
        <li><a href="dart-core/Uri/tryParse.html">tryParse</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-property">port</span> property</h1></div>


        <section id="getter">
        
        <section class="multi-line-signature">
          <span class="returntype"><a href="dart-core/int-class.html">int</a></span>
                  <span class="name ">port</span>
          
</section>
        
                <section class="desc markdown">
          <p>The port part of the authority component.</p>
<p>The value is the default port if there is no port number in the authority
component. That's 80 for http, 443 for https, and 0 for everything else.</p>
        </section>
                <section class="summary source-code" id="source">
          <h2><span>Implementation</span></h2>
          <pre class="language-dart"><code class="language-dart">int get port;</code></pre>
        </section>
</section>
        
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
