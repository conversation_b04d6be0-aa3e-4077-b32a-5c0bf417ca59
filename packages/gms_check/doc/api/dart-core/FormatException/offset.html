<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the offset property from the FormatException class, for the Dart programming language.">
  <title>offset property - FormatException class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
    <li class="self-crumb">offset property</li>
  </ol>
  <div class="self-name">offset</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/FormatException-class.html">FormatException</a></li>
      <li class="self-crumb">offset property</li>
    </ol>
    
    <h5>FormatException class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/FormatException-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/FormatException/FormatException.html">FormatException</a></li>
    
        <li class="section-title">
            <a href="dart-core/FormatException-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/FormatException/message.html">message</a></li>
        <li><a href="dart-core/FormatException/offset.html">offset</a></li>
        <li><a href="dart-core/FormatException/source.html">source</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/FormatException-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/FormatException/toString.html">toString</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
        <li class="section-title inherited"><a href="dart-core/FormatException-class.html#operators">Operators</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-property">offset</span> property</h1></div>

        <section class="multi-line-signature">
          <span class="returntype"><a href="dart-core/int-class.html">int</a></span>
          <span class="name ">offset</span>
          <div class="features">final</div>
        </section>
        <section class="desc markdown">
          <p>The offset in <a href="dart-core/FormatException/source.html">source</a> where the error was detected.</p>
<p>A zero-based offset into the source that marks the format error causing
this exception to be created. If <code>source</code> is a string, this should be a
string index in the range <code>0 &lt;= offset &lt;= source.length</code>.</p>
<p>If input is a string, the <a href="dart-core/FormatException/toString.html">toString</a> method may represent this offset as
a line and character position. The offset should be inside the string,
or at the end of the string.</p>
<p>May be omitted. If present, <a href="dart-core/FormatException/source.html">source</a> should also be present if possible.</p>
        </section>
                <section class="summary source-code" id="source">
          <h2><span>Implementation</span></h2>
          <pre class="language-dart"><code class="language-dart">final int? offset

</code></pre>
        </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
