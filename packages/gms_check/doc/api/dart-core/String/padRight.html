<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the padRight method from the String class, for the Dart programming language.">
  <title>padRight method - String class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/String-class.html">String</a></li>
    <li class="self-crumb">padRight abstract method</li>
  </ol>
  <div class="self-name">padRight</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/String-class.html">String</a></li>
      <li class="self-crumb">padRight abstract method</li>
    </ol>
    
    <h5>String class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/String-class.html#constructors">Constructors</a></li>
        <li><a href="dart-core/String/String.fromCharCode.html">fromCharCode</a></li>
        <li><a href="dart-core/String/String.fromCharCodes.html">fromCharCodes</a></li>
        <li><a href="dart-core/String/String.fromEnvironment.html">fromEnvironment</a></li>
    
        <li class="section-title">
            <a href="dart-core/String-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/String/codeUnits.html">codeUnits</a></li>
        <li><a href="dart-core/String/hashCode.html">hashCode</a></li>
        <li><a href="dart-core/String/isEmpty.html">isEmpty</a></li>
        <li><a href="dart-core/String/isNotEmpty.html">isNotEmpty</a></li>
        <li><a href="dart-core/String/length.html">length</a></li>
        <li><a href="dart-core/String/runes.html">runes</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-core/String-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/String/codeUnitAt.html">codeUnitAt</a></li>
        <li><a href="dart-core/String/compareTo.html">compareTo</a></li>
        <li><a href="dart-core/String/contains.html">contains</a></li>
        <li><a href="dart-core/String/endsWith.html">endsWith</a></li>
        <li><a href="dart-core/String/indexOf.html">indexOf</a></li>
        <li><a href="dart-core/String/lastIndexOf.html">lastIndexOf</a></li>
        <li><a href="dart-core/String/padLeft.html">padLeft</a></li>
        <li><a href="dart-core/String/padRight.html">padRight</a></li>
        <li><a href="dart-core/String/replaceAll.html">replaceAll</a></li>
        <li><a href="dart-core/String/replaceAllMapped.html">replaceAllMapped</a></li>
        <li><a href="dart-core/String/replaceFirst.html">replaceFirst</a></li>
        <li><a href="dart-core/String/replaceFirstMapped.html">replaceFirstMapped</a></li>
        <li><a href="dart-core/String/replaceRange.html">replaceRange</a></li>
        <li><a href="dart-core/String/split.html">split</a></li>
        <li><a href="dart-core/String/splitMapJoin.html">splitMapJoin</a></li>
        <li><a href="dart-core/String/startsWith.html">startsWith</a></li>
        <li><a href="dart-core/String/substring.html">substring</a></li>
        <li><a href="dart-core/String/toLowerCase.html">toLowerCase</a></li>
        <li><a href="dart-core/String/toUpperCase.html">toUpperCase</a></li>
        <li><a href="dart-core/String/trim.html">trim</a></li>
        <li><a href="dart-core/String/trimLeft.html">trimLeft</a></li>
        <li><a href="dart-core/String/trimRight.html">trimRight</a></li>
        <li class="inherited"><a href="dart-core/Pattern/allMatches.html">allMatches</a></li>
        <li class="inherited"><a href="dart-core/Pattern/matchAsPrefix.html">matchAsPrefix</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title"><a href="dart-core/String-class.html#operators">Operators</a></li>
        <li><a href="dart-core/String/operator_multiply.html">operator *</a></li>
        <li><a href="dart-core/String/operator_plus.html">operator +</a></li>
        <li><a href="dart-core/String/operator_equals.html">operator ==</a></li>
        <li><a href="dart-core/String/operator_get.html">operator []</a></li>
    
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">padRight</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-core/String-class.html">String</a></span>
            <span class="name ">padRight</span>
(<wbr><span class="parameter" id="padRight-param-width"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">width</span>, [</span> <span class="parameter" id="padRight-param-padding"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">padding</span> = <span class="default-value">' '</span></span> ])
      
    </section>
    <section class="desc markdown">
      <p>Pads this string on the right if it is shorter than <code>width</code>.</p>
<p>Returns a new string that appends <code>padding</code> after this string
one time for each position the length is less than <code>width</code>.</p>
<p>If <code>width</code> is already smaller than or equal to <code>this.length</code>,
no padding is added. A negative <code>width</code> is treated as zero.</p>
<p>If <code>padding</code> has length different from 1, the result will not
have length <code>width</code>. This may be useful for cases where the
padding is a longer string representing a single character, like
<code>"&amp;nbsp;"</code> or <code>"\u{10002}</code>".
In that case, the user should make sure that <code>this.length</code> is
the correct measure of the strings length.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">String padRight(int width, [String padding = &#39; &#39;]);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
