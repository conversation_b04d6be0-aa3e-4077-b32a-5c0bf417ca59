<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the StreamView constructor from the Class StreamView class from the dart:async library, for the Dart programming language.">
  <title>StreamView constructor - StreamView class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/StreamView-class.html">StreamView<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
    <li class="self-crumb">StreamView const constructor</li>
  </ol>
  <div class="self-name">StreamView</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/StreamView-class.html">StreamView<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
      <li class="self-crumb">StreamView const constructor</li>
    </ol>
    
    <h5>StreamView class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-async/StreamView-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/StreamView/StreamView.html">StreamView</a></li>
    
      <li class="section-title">
        <a href="dart-async/StreamView-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-async/StreamView/isBroadcast.html">isBroadcast</a></li>
      <li class="inherited"><a href="dart-async/Stream/first.html">first</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-async/Stream/isEmpty.html">isEmpty</a></li>
      <li class="inherited"><a href="dart-async/Stream/last.html">last</a></li>
      <li class="inherited"><a href="dart-async/Stream/length.html">length</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
      <li class="inherited"><a href="dart-async/Stream/single.html">single</a></li>
    
      <li class="section-title"><a href="dart-async/StreamView-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-async/StreamView/asBroadcastStream.html">asBroadcastStream</a></li>
      <li><a href="dart-async/StreamView/listen.html">listen</a></li>
      <li class="inherited"><a href="dart-async/Stream/any.html">any</a></li>
      <li class="inherited"><a href="dart-async/Stream/asyncExpand.html">asyncExpand</a></li>
      <li class="inherited"><a href="dart-async/Stream/asyncMap.html">asyncMap</a></li>
      <li class="inherited"><a href="dart-async/Stream/cast.html">cast</a></li>
      <li class="inherited"><a href="dart-async/Stream/contains.html">contains</a></li>
      <li class="inherited"><a href="dart-async/Stream/distinct.html">distinct</a></li>
      <li class="inherited"><a href="dart-async/Stream/drain.html">drain</a></li>
      <li class="inherited"><a href="dart-async/Stream/elementAt.html">elementAt</a></li>
      <li class="inherited"><a href="dart-async/Stream/every.html">every</a></li>
      <li class="inherited"><a href="dart-async/Stream/expand.html">expand</a></li>
      <li class="inherited"><a href="dart-async/Stream/firstWhere.html">firstWhere</a></li>
      <li class="inherited"><a href="dart-async/Stream/fold.html">fold</a></li>
      <li class="inherited"><a href="dart-async/Stream/forEach.html">forEach</a></li>
      <li class="inherited"><a href="dart-async/Stream/handleError.html">handleError</a></li>
      <li class="inherited"><a href="dart-async/Stream/join.html">join</a></li>
      <li class="inherited"><a href="dart-async/Stream/lastWhere.html">lastWhere</a></li>
      <li class="inherited"><a href="dart-async/Stream/map.html">map</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-async/Stream/pipe.html">pipe</a></li>
      <li class="inherited"><a href="dart-async/Stream/reduce.html">reduce</a></li>
      <li class="inherited"><a href="dart-async/Stream/singleWhere.html">singleWhere</a></li>
      <li class="inherited"><a href="dart-async/Stream/skip.html">skip</a></li>
      <li class="inherited"><a href="dart-async/Stream/skipWhile.html">skipWhile</a></li>
      <li class="inherited"><a href="dart-async/Stream/take.html">take</a></li>
      <li class="inherited"><a href="dart-async/Stream/takeWhile.html">takeWhile</a></li>
      <li class="inherited"><a href="dart-async/Stream/timeout.html">timeout</a></li>
      <li class="inherited"><a href="dart-async/Stream/toList.html">toList</a></li>
      <li class="inherited"><a href="dart-async/Stream/toSet.html">toSet</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-async/Stream/transform.html">transform</a></li>
      <li class="inherited"><a href="dart-async/Stream/where.html">where</a></li>
    
      <li class="section-title inherited"><a href="dart-async/StreamView-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">StreamView&lt;<wbr><span class="type-parameter">T</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      const
      <span class="name ">StreamView&lt;<wbr><span class="type-parameter">T</span>&gt;</span>(<wbr><span class="parameter" id="-param-stream"><span class="type-annotation"><a href="dart-async/Stream-class.html">Stream</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">stream</span></span>)
    </section>

    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">const StreamView(Stream&lt;T&gt; stream)
    : _stream = stream,
      super._internal();</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
