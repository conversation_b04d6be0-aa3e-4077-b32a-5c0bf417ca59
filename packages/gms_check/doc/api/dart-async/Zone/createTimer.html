<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the createTimer method from the Zone class, for the Dart programming language.">
  <title>createTimer method - Zone class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/Zone-class.html">Zone</a></li>
    <li class="self-crumb">createTimer abstract method</li>
  </ol>
  <div class="self-name">createTimer</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/Zone-class.html">Zone</a></li>
      <li class="self-crumb">createTimer abstract method</li>
    </ol>
    
    <h5>Zone class</h5>
    <ol>
    
    
        <li class="section-title">
            <a href="dart-async/Zone-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-async/Zone/errorZone.html">errorZone</a></li>
        <li><a href="dart-async/Zone/parent.html">parent</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
        <li class="section-title"><a href="dart-async/Zone-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-async/Zone/bindBinaryCallback.html">bindBinaryCallback</a></li>
        <li><a href="dart-async/Zone/bindBinaryCallbackGuarded.html">bindBinaryCallbackGuarded</a></li>
        <li><a href="dart-async/Zone/bindCallback.html">bindCallback</a></li>
        <li><a href="dart-async/Zone/bindCallbackGuarded.html">bindCallbackGuarded</a></li>
        <li><a href="dart-async/Zone/bindUnaryCallback.html">bindUnaryCallback</a></li>
        <li><a href="dart-async/Zone/bindUnaryCallbackGuarded.html">bindUnaryCallbackGuarded</a></li>
        <li><a href="dart-async/Zone/createPeriodicTimer.html">createPeriodicTimer</a></li>
        <li><a href="dart-async/Zone/createTimer.html">createTimer</a></li>
        <li><a href="dart-async/Zone/errorCallback.html">errorCallback</a></li>
        <li><a href="dart-async/Zone/fork.html">fork</a></li>
        <li><a href="dart-async/Zone/handleUncaughtError.html">handleUncaughtError</a></li>
        <li><a href="dart-async/Zone/inSameErrorZone.html">inSameErrorZone</a></li>
        <li><a href="dart-async/Zone/print.html">print</a></li>
        <li><a href="dart-async/Zone/registerBinaryCallback.html">registerBinaryCallback</a></li>
        <li><a href="dart-async/Zone/registerCallback.html">registerCallback</a></li>
        <li><a href="dart-async/Zone/registerUnaryCallback.html">registerUnaryCallback</a></li>
        <li><a href="dart-async/Zone/run.html">run</a></li>
        <li><a href="dart-async/Zone/runBinary.html">runBinary</a></li>
        <li><a href="dart-async/Zone/runBinaryGuarded.html">runBinaryGuarded</a></li>
        <li><a href="dart-async/Zone/runGuarded.html">runGuarded</a></li>
        <li><a href="dart-async/Zone/runUnary.html">runUnary</a></li>
        <li><a href="dart-async/Zone/runUnaryGuarded.html">runUnaryGuarded</a></li>
        <li><a href="dart-async/Zone/scheduleMicrotask.html">scheduleMicrotask</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
        <li class="section-title"><a href="dart-async/Zone-class.html#operators">Operators</a></li>
        <li><a href="dart-async/Zone/operator_get.html">operator []</a></li>
        <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
        <li class="section-title"><a href="dart-async/Zone-class.html#static-properties">Static properties</a></li>
        <li><a href="dart-async/Zone/current.html">current</a></li>
    
    
        <li class="section-title"><a href="dart-async/Zone-class.html#constants">Constants</a></li>
        <li><a href="dart-async/Zone/root-constant.html">root</a></li>
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">createTimer</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype"><a href="dart-async/Timer-class.html">Timer</a></span>
            <span class="name ">createTimer</span>
(<wbr><span class="parameter" id="createTimer-param-duration"><span class="type-annotation"><a href="dart-core/Duration-class.html">Duration</a></span> <span class="parameter-name">duration</span>, </span> <span class="parameter" id="createTimer-param-callback"><span class="type-annotation">void</span> <span class="parameter-name">callback</span>()</span>)
      
    </section>
    <section class="desc markdown">
      <p>Creates a <a href="dart-async/Timer-class.html">Timer</a> where the callback is executed in this zone.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">Timer createTimer(Duration duration, void Function() callback);</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
