<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the ZoneSpecification.from constructor from the Class ZoneSpecification class from the dart:async library, for the Dart programming language.">
  <title>ZoneSpecification.from constructor - ZoneSpecification class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></li>
    <li class="self-crumb">ZoneSpecification.from factory constructor</li>
  </ol>
  <div class="self-name">ZoneSpecification.from</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></li>
      <li class="self-crumb">ZoneSpecification.from factory constructor</li>
    </ol>
    
    <h5>ZoneSpecification class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-async/ZoneSpecification-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/ZoneSpecification/ZoneSpecification.html">ZoneSpecification</a></li>
      <li><a href="dart-async/ZoneSpecification/ZoneSpecification.from.html">from</a></li>
    
      <li class="section-title">
        <a href="dart-async/ZoneSpecification-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-async/ZoneSpecification/createPeriodicTimer.html">createPeriodicTimer</a></li>
      <li><a href="dart-async/ZoneSpecification/createTimer.html">createTimer</a></li>
      <li><a href="dart-async/ZoneSpecification/errorCallback.html">errorCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/fork.html">fork</a></li>
      <li><a href="dart-async/ZoneSpecification/handleUncaughtError.html">handleUncaughtError</a></li>
      <li><a href="dart-async/ZoneSpecification/print.html">print</a></li>
      <li><a href="dart-async/ZoneSpecification/registerBinaryCallback.html">registerBinaryCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/registerCallback.html">registerCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/registerUnaryCallback.html">registerUnaryCallback</a></li>
      <li><a href="dart-async/ZoneSpecification/run.html">run</a></li>
      <li><a href="dart-async/ZoneSpecification/runBinary.html">runBinary</a></li>
      <li><a href="dart-async/ZoneSpecification/runUnary.html">runUnary</a></li>
      <li><a href="dart-async/ZoneSpecification/scheduleMicrotask.html">scheduleMicrotask</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title inherited"><a href="dart-async/ZoneSpecification-class.html#instance-methods">Methods</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-async/ZoneSpecification-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">ZoneSpecification.from</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">ZoneSpecification.from</span>(<wbr><span class="parameter" id="from-param-other"><span class="type-annotation"><a href="dart-async/ZoneSpecification-class.html">ZoneSpecification</a></span> <span class="parameter-name">other</span>, {</span> <span class="parameter" id="from-param-handleUncaughtError"><span class="type-annotation"><a href="dart-async/HandleUncaughtErrorHandler.html">HandleUncaughtErrorHandler</a></span> <span class="parameter-name">handleUncaughtError</span>, </span> <span class="parameter" id="from-param-run"><span class="type-annotation">R</span> <span class="parameter-name">run</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>), </span> <span class="parameter" id="from-param-runUnary"><span class="type-annotation">R</span> <span class="parameter-name">runUnary</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>), </span> <span class="parameter" id="from-param-runBinary"><span class="type-annotation">R</span> <span class="parameter-name">runBinary</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>), </span> <span class="parameter" id="from-param-registerCallback"><span class="type-annotation"><a href="dart-async/ZoneCallback.html">ZoneCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>&gt;</span></span> <span class="parameter-name">registerCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>()</span>), </span> <span class="parameter" id="from-param-registerUnaryCallback"><span class="type-annotation"><a href="dart-async/ZoneUnaryCallback.html">ZoneUnaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">registerUnaryCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg"><span class="type-annotation">T</span> <span class="parameter-name">arg</span></span>)</span>), </span> <span class="parameter" id="from-param-registerBinaryCallback"><span class="type-annotation"><a href="dart-async/ZoneBinaryCallback.html">ZoneBinaryCallback</a><span class="signature">&lt;<wbr><span class="type-parameter">R</span>, <span class="type-parameter">T1</span>, <span class="type-parameter">T2</span>&gt;</span></span> <span class="parameter-name">registerBinaryCallback</span>(<span class="parameter" id="param-self"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">self</span>, </span> <span class="parameter" id="param-parent"><span class="type-annotation"><a href="dart-async/ZoneDelegate-class.html">ZoneDelegate</a></span> <span class="parameter-name">parent</span>, </span> <span class="parameter" id="param-zone"><span class="type-annotation"><a href="dart-async/Zone-class.html">Zone</a></span> <span class="parameter-name">zone</span>, </span> <span class="parameter" id="param-f"><span class="type-annotation">R</span> <span class="parameter-name">f</span>(<span class="parameter" id="param-arg1"><span class="type-annotation">T1</span> <span class="parameter-name">arg1</span>, </span> <span class="parameter" id="param-arg2"><span class="type-annotation">T2</span> <span class="parameter-name">arg2</span></span>)</span>), </span> <span class="parameter" id="from-param-errorCallback"><span class="type-annotation"><a href="dart-async/ErrorCallbackHandler.html">ErrorCallbackHandler</a></span> <span class="parameter-name">errorCallback</span>, </span> <span class="parameter" id="from-param-scheduleMicrotask"><span class="type-annotation"><a href="dart-async/ScheduleMicrotaskHandler.html">ScheduleMicrotaskHandler</a></span> <span class="parameter-name">scheduleMicrotask</span>, </span> <span class="parameter" id="from-param-createTimer"><span class="type-annotation"><a href="dart-async/CreateTimerHandler.html">CreateTimerHandler</a></span> <span class="parameter-name">createTimer</span>, </span> <span class="parameter" id="from-param-createPeriodicTimer"><span class="type-annotation"><a href="dart-async/CreatePeriodicTimerHandler.html">CreatePeriodicTimerHandler</a></span> <span class="parameter-name">createPeriodicTimer</span>, </span> <span class="parameter" id="from-param-print"><span class="type-annotation"><a href="dart-async/PrintHandler.html">PrintHandler</a></span> <span class="parameter-name">print</span>, </span> <span class="parameter" id="from-param-fork"><span class="type-annotation"><a href="dart-async/ForkHandler.html">ForkHandler</a></span> <span class="parameter-name">fork</span></span> })
    </section>

    <section class="desc markdown">
      <p>Creates a specification from <code>other</code> and provided handlers.</p>
<p>The created zone specification has the handlers of <code>other</code>
and any individually provided handlers.
If a handler is provided both through <code>other</code> and individually,
the individually provided handler overrides the one from <code>other</code>.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory ZoneSpecification.from(ZoneSpecification other,
    {HandleUncaughtErrorHandler? handleUncaughtError,
    RunHandler? run,
    RunUnaryHandler? runUnary,
    RunBinaryHandler? runBinary,
    RegisterCallbackHandler? registerCallback,
    RegisterUnaryCallbackHandler? registerUnaryCallback,
    RegisterBinaryCallbackHandler? registerBinaryCallback,
    ErrorCallbackHandler? errorCallback,
    ScheduleMicrotaskHandler? scheduleMicrotask,
    CreateTimerHandler? createTimer,
    CreatePeriodicTimerHandler? createPeriodicTimer,
    PrintHandler? print,
    ForkHandler? fork}) {
  return new ZoneSpecification(
      handleUncaughtError: handleUncaughtError ?? other.handleUncaughtError,
      run: run ?? other.run,
      runUnary: runUnary ?? other.runUnary,
      runBinary: runBinary ?? other.runBinary,
      registerCallback: registerCallback ?? other.registerCallback,
      registerUnaryCallback:
          registerUnaryCallback ?? other.registerUnaryCallback,
      registerBinaryCallback:
          registerBinaryCallback ?? other.registerBinaryCallback,
      errorCallback: errorCallback ?? other.errorCallback,
      scheduleMicrotask: scheduleMicrotask ?? other.scheduleMicrotask,
      createTimer: createTimer ?? other.createTimer,
      createPeriodicTimer: createPeriodicTimer ?? other.createPeriodicTimer,
      print: print ?? other.print,
      fork: fork ?? other.fork);
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
